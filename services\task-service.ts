import { firestore } from '@/config/firebase';
import { collection, doc, addDoc, updateDoc, deleteDoc, getDoc, getDocs, query, where, orderBy } from 'firebase/firestore';
import { Task, TaskStatus, TaskRecurrence } from '@/types/task';

/**
 * Add a new task to a farm's tasks subcollection
 */
export const addTask = async (taskData: Partial<Task>): Promise<string> => {
  try {
    if (!taskData.farmId) {
      throw new Error('farmId is required to add a task');
    }

    // Add to Firestore as subcollection under farm
    const farmRef = doc(firestore, 'farms', taskData.farmId);
    const tasksCollectionRef = collection(farmRef, 'tasks');
    const taskRef = await addDoc(tasksCollectionRef, {
      ...taskData,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: taskData.status || 'pending'
    });

    return taskRef.id;
  } catch (error) {
    console.error('Error adding task:', error);
    throw error;
  }
};

/**
 * Update an existing task in a farm's tasks subcollection
 */
export const updateTask = async (taskId: string, farmId: string, updates: Partial<Task>): Promise<void> => {
  try {
    if (!farmId) {
      throw new Error('farmId is required to update a task');
    }

    const farmRef = doc(firestore, 'farms', farmId);
    const taskRef = doc(farmRef, 'tasks', taskId);

    // Get the current task data
    const taskDoc = await getDoc(taskRef);
    if (!taskDoc.exists()) {
      throw new Error('Task not found');
    }

    const currentTask = { id: taskDoc.id, ...taskDoc.data() } as Task;

    // Update the task
    await updateDoc(taskRef, {
      ...updates,
      updatedAt: new Date()
    });

    // If task is being marked as completed and it's a recurring task, create the next occurrence
    if (
      updates.status === TaskStatus.COMPLETED &&
      currentTask.recurrence !== TaskRecurrence.NONE
    ) {
      await createNextRecurringTask(currentTask);
    }

  } catch (error) {
    console.error('Error updating task:', error);
    throw error;
  }
};

/**
 * Delete a task from a farm's tasks subcollection
 */
export const deleteTask = async (taskId: string, farmId: string): Promise<void> => {
  try {
    if (!farmId) {
      throw new Error('farmId is required to delete a task');
    }

    const farmRef = doc(firestore, 'farms', farmId);
    const taskRef = doc(farmRef, 'tasks', taskId);
    await deleteDoc(taskRef);
  } catch (error) {
    console.error('Error deleting task:', error);
    throw error;
  }
};

/**
 * Get tasks based on user role and ID from farm subcollections
 * - Owners see tasks for their farms only
 * - Admins see tasks for farms they're assigned to
 * - Caretakers only see tasks assigned to them
 */
export const getTasks = async (userId: string, userRole: string): Promise<Task[]> => {
  try {
    // First, get the user's data to determine their assigned farms
    const userRef = doc(firestore, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data();
    let farmIds: string[] = [];

    // Get assigned farm IDs for all user roles
    const assignedFarmIds = userData.assignedFarmIds || [];

    if (userRole === 'owner') {
      // For owners, if no assignedFarmIds, fall back to querying by ownerId for backward compatibility
      if (assignedFarmIds.length > 0) {
        farmIds = assignedFarmIds;
      } else {
        // Fallback: get farms they own by ownerId
        const farmsRef = collection(firestore, 'farms');
        const q = query(farmsRef, where("ownerId", "==", userId));
        const querySnapshot = await getDocs(q);
        farmIds = querySnapshot.docs.map(doc => doc.id);
      }
    } else if (userRole === 'admin' || userRole === 'caretaker') {
      // For employees, use assignedFarmIds
      farmIds = assignedFarmIds;
    }

    if (farmIds.length === 0) {
      return []; // No farms assigned, no tasks
    }

    // Fetch tasks from each farm's subcollection
    const allTasks: Task[] = [];

    for (const farmId of farmIds) {
      try {
        const farmRef = doc(firestore, 'farms', farmId);
        const tasksCollectionRef = collection(farmRef, 'tasks');

        let tasksQuery;

        // Apply role-based filtering
        if (userRole === 'owner' || userRole === 'admin') {
          // Owners and admins see tasks they created for their farms
          tasksQuery = query(tasksCollectionRef, where('assignedBy', '==', userId));
        } else {
          // Caretakers only see tasks assigned to them
          tasksQuery = query(tasksCollectionRef, where('assignedTo', '==', userId));
        }

        const tasksSnapshot = await getDocs(tasksQuery);

        tasksSnapshot.forEach((doc) => {
          allTasks.push({
            id: doc.id,
            ...doc.data(),
            farmId: farmId // Ensure farmId is set
          } as Task);
        });
      } catch (farmError) {
        // Continue with other farms even if one fails
        console.error(`Error fetching tasks from farm ${farmId}:`, farmError);
      }
    }

    // Sort the tasks by due date
    allTasks.sort((a, b) => {
      const dateA = a.dueDate instanceof Date ? a.dueDate.getTime() : a.dueDate;
      const dateB = b.dueDate instanceof Date ? b.dueDate.getTime() : b.dueDate;
      return dateA - dateB;
    });

    return allTasks;
  } catch (error) {
    console.error('Error getting tasks:', error);
    throw error;
  }
};

/**
 * Create the next occurrence of a recurring task
 */
const createNextRecurringTask = async (completedTask: Task): Promise<void> => {
  try {
    // Calculate the next due date based on recurrence type
    const nextDueDate = calculateNextDueDate(
      completedTask.dueDate,
      completedTask.recurrence
    );
    
    // Create a new task with the same details but updated due date
    const newTaskData: Partial<Task> = {
      title: completedTask.title,
      description: completedTask.description,
      farmId: completedTask.farmId,
      farmName: completedTask.farmName,
      assignedTo: completedTask.assignedTo,
      assigneeName: completedTask.assigneeName,
      assignedBy: completedTask.assignedBy,
      assignedByName: completedTask.assignedByName,
      dueDate: nextDueDate,
      priority: completedTask.priority,
      status: TaskStatus.PENDING,
      notes: completedTask.notes,
      recurrence: completedTask.recurrence,
      parent_task_id: completedTask.id,
      related_animal_id: completedTask.related_animal_id,
      related_animal_name: completedTask.related_animal_name,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await addTask(newTaskData);
    
  } catch (error) {
    console.error('Error creating next recurring task:', error);
    throw error;
  }
};

/**
 * Calculate the next due date based on recurrence type
 */
const calculateNextDueDate = (currentDueDate: number, recurrence: TaskRecurrence): number => {
  const currentDate = new Date(currentDueDate);
  
  switch (recurrence) {
    case TaskRecurrence.DAILY:
      // Add 1 day
      return currentDate.setDate(currentDate.getDate() + 1);
      
    case TaskRecurrence.WEEKLY:
      // Add 7 days
      return currentDate.setDate(currentDate.getDate() + 7);
      
    case TaskRecurrence.MONTHLY:
      // Add 1 month (handle month rollover)
      const nextMonth = currentDate.getMonth() + 1;
      return currentDate.setMonth(nextMonth);
      
    default:
      // Should not happen, but return current date + 1 day as fallback
      return currentDate.setDate(currentDate.getDate() + 1);
  }
};


