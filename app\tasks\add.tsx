import React, { useState, useEffect, useMemo } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  Alert,
  Platform,
  ActivityIndicator,
  Image
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Audio } from 'expo-av';
import { 
  Calendar, 
  AlertCircle, 
  User,
  MapPin,
  Repeat,
  Mic,
  MicOff,
  Save
} from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { useLookupStore } from '@/store/lookup-store';
import { Task, TaskStatus, TaskPriority, TaskRecurrence } from '@/types/task';
import { addTask } from '@/services/task-service';
import * as Speech from 'expo-speech';
import { startSpeechRecognition, stopSpeechRecognition, setCurrentField } from '@/services/speech-service';
import GenericDropdown from '@/components/GenericDropdown';
import { getEmployeesByFarm } from '@/services/employee-service';
import { getStoredLanguage, getSpeechLanguageCode } from '@/services/language-service';
import { getOpenAIApiKey } from '@/services/settings-service';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { useThemeColors } from '@/hooks/useThemeColors';

const AddTaskScreen: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { farms } = useFarmStore();
  const { playFeedback } = useAudioFeedback();
  const { getLookupsByCategory } = useLookupStore();
  const themedColors = useThemeColors();

  const styles = getStyles(themedColors);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [farmId, setFarmId] = useState('');
  const [farmName, setFarmName] = useState('');
  const [assigneeId, setAssigneeId] = useState('');
  const [assigneeName, setAssigneeName] = useState('');
  const [dueDate, setDueDate] = useState(new Date());
  const [priority, setPriority] = useState<TaskPriority>(TaskPriority.MEDIUM);
  const [notes, setNotes] = useState('');
  const [recurrence, setRecurrence] = useState<TaskRecurrence>(TaskRecurrence.NONE);
  
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [employees, setEmployees] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingEmployees, setLoadingEmployees] = useState(false);
  
  const [isRecordingDescription, setIsRecordingDescription] = useState(false);
  const [isRecordingNotes, setIsRecordingNotes] = useState(false);
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false);
  const [currentField, setCurrentField] = useState<'description' | 'notes' | null>(null);

  const [openaiApiKey, setOpenaiApiKey] = useState<string | null>(null);

  useEffect(() => {
    const initAudio = async () => {
      // Skip audio initialization on web platform
      if (Platform.OS === 'web') return;
      
      try {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
        });
      } catch (error) {
        console.error('Error setting audio mode:', error);
      }
    };
    
    initAudio();
    
    return () => {
    };
  }, []);

  useEffect(() => {
    if (farmId) {
      loadEmployees();
    } else {
      setEmployees([]);
      setAssigneeId('');
      setAssigneeName('');
    }
  }, [farmId]);

  const loadEmployees = async () => {
    try {
      setLoadingEmployees(true);
      setAssigneeId('');
      setAssigneeName('');
      
      const farmEmployees = await getEmployeesByFarm(farmId);
      
      if (Array.isArray(farmEmployees) && farmEmployees.length > 0) {
        setEmployees(farmEmployees);
      } else {
        setEmployees([]);
      }
    } catch (error) {
      console.error('Error loading employees:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
      setEmployees([]);
    } finally {
      setLoadingEmployees(false);
    }
  };

  useEffect(() => {
    const loadApiKey = async () => {
      const apiKey = await getOpenAIApiKey();
      setOpenaiApiKey(apiKey);
    };
    
    loadApiKey();
  }, []);

  const startRecording = async (field: 'description' | 'notes') => {
    // Show unsupported message on web platform
    if (Platform.OS === 'web') {
      Alert.alert(
        t('common.unsupported'),
        t('common.voiceRecordingWebUnsupported')
      );
      return;
    }

    try {
      setCurrentField(field);
      if (field === 'description') {
        setIsRecordingDescription(true);
        setIsRecordingNotes(false);
      } else {
        setIsRecordingDescription(false);
        setIsRecordingNotes(true);
      }

      setIsProcessingSpeech(true);
      
      try {
        // Get the current app language
        const appLanguage = await getStoredLanguage();
        // Get the appropriate speech language code (ur-PK for Urdu)
        const speechLanguage = getSpeechLanguageCode(appLanguage);
        
        // Set the current field in the speech service
        setCurrentField(field);
        
        // Start recording with the correct language
        await startSpeechRecognition({ language: speechLanguage });
      } catch (error) {
        console.error('Error starting speech recognition:', error);
        Alert.alert(t('common.error'), t('common.speechRecognitionError'));
        setIsProcessingSpeech(false);
        setIsRecordingDescription(false);
        setIsRecordingNotes(false);
        setCurrentField(null);
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
      stopRecording();
    }
  };

  const stopRecording = async () => {
    try {
      if (isRecordingDescription || isRecordingNotes) {
        setIsProcessingSpeech(true);
        
        try {
          // Stop recording and get the transcribed text
          const transcribedText = await stopSpeechRecognition();
          
          // Update the appropriate field with the transcribed text
          if (isRecordingDescription) {
            setDescription(transcribedText);
          } else if (isRecordingNotes) {
            setNotes(transcribedText);
          }
          
          // Play success sound
          playFeedback('success');
          
          // Show success message
          Alert.alert(t('common.success'), t('common.recordingSaved'));
        } catch (error) {
          console.error('Error stopping speech recognition:', error);
          Alert.alert(t('common.error'), t('common.speechRecognitionError'));
        } finally {
          setIsProcessingSpeech(false);
          setIsRecordingDescription(false);
          setIsRecordingNotes(false);
          setCurrentField(null);
        }
      }
    } catch (error) {
      console.error('Error stopping recording:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
      setIsProcessingSpeech(false);
      setIsRecordingDescription(false);
      setIsRecordingNotes(false);
      setCurrentField(null);
    }
  };

  const validateForm = () => {
    if (!title.trim()) {
      Alert.alert(t('common.error'), t('tasks.errors.titleRequired'));
      return false;
    }
    if (!farmId) {
      Alert.alert(t('common.error'), t('tasks.errors.farmRequired'));
      return false;
    }
    if (!assigneeId) {
      Alert.alert(t('common.error'), t('tasks.errors.assigneeRequired'));
      return false;
    }
    return true;
  };

  const handleFarmSelect = (id: string) => {
    const selectedFarm = farms.find(farm => farm.id === id);
    if (selectedFarm) {
      setFarmId(selectedFarm.id);
      setFarmName(selectedFarm.name);
    }
  };

  const handleAssigneeSelect = (id: string) => {
    const selectedEmployee = employees.find(emp => emp.id === id);
    if (selectedEmployee) {
      setAssigneeId(selectedEmployee.id);
      setAssigneeName(selectedEmployee.name);
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };

  const handlePrioritySelect = (value: string) => {
    setPriority(value as TaskPriority);
  };

  const handleRecurrenceSelect = (value: string) => {
    setRecurrence(value as TaskRecurrence);
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    if (!user) {
      Alert.alert(t('common.error'), t('common.notLoggedIn'));
      return;
    }

    setIsLoading(true);
    try {
      const taskData = {
        title,
        description,
        farmId,
        farmName,
        assignedTo: assigneeId,
        assigneeName,
        assignedBy: user.id,
        assignedByName: user.name,
        dueDate: dueDate.getTime(),
        priority,
        status: TaskStatus.PENDING,
        notes,
        recurrence,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await addTask(taskData);
      router.replace('/(tabs)/tasks');
      Alert.alert(t('common.success'), t('tasks.addSuccess'));
    } catch (error) {
      console.error('Error adding task:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
    } finally {
      setIsLoading(false);
    }
  };

  const priorityOptions = useMemo(() => {
    const priorityLookups = getLookupsByCategory('taskPriority');
    if (!priorityLookups) return [];

    const getIcon = (priority: string) => {
      switch (priority.toLowerCase()) {
        case TaskPriority.HIGH:
          return <AlertCircle size={20} color={colors.error} />;
        case TaskPriority.MEDIUM:
          return <AlertCircle size={20} color={colors.warning} />;
        case TaskPriority.LOW:
          return <AlertCircle size={20} color={colors.success} />;
        default:
          return <AlertCircle size={20} color={colors.textSecondary} />;
      }
    };

    return priorityLookups.map(lookup => ({
      id: lookup.title.toLowerCase() as TaskPriority,
      label: t(`tasks.priorityValue.${lookup.title.toLowerCase()}`),
      icon: getIcon(lookup.title)
    }));
  }, [getLookupsByCategory, t]);

  const recurrenceOptions = useMemo(() => {
    const recurrenceLookups = getLookupsByCategory('taskFrequency');
    if (!recurrenceLookups) return [];

    const getIcon = (recurrence: string) => {
      const r = recurrence.toLowerCase();
      if (r === TaskRecurrence.ONCE) {
        return <AlertCircle size={20} color={colors.textSecondary} />;
      }
      if (
        r === TaskRecurrence.DAILY ||
        r === TaskRecurrence.WEEKLY ||
        r === TaskRecurrence.MONTHLY
      ) {
        return <Repeat size={20} color={colors.primary} />;
      }
      return null;
    };

    const data =  recurrenceLookups.map(lookup => ({
      id: lookup.title.toLowerCase() as TaskRecurrence,
      label: t(`farms.tasks.recurrenceValues.${lookup.title.toLowerCase()}`),
      icon: getIcon(lookup.title)
    }));
    console.log("recurrenceOptions",data)
    return data;
  }, [getLookupsByCategory, t]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('tasks.addTask'),
          headerBackTitle: t('common.back')
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('tasks.title')}*</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder={t('tasks.titlePlaceholder')}
            placeholderTextColor={colors.textSecondary}
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('tasks.description')}</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={[
                styles.input, 
                styles.textArea, 
                styles.inputWithButton,
                isRecordingDescription && styles.inputRecording
              ]}
              value={description}
              onChangeText={setDescription}
              placeholder={t('tasks.descriptionPlaceholder')}
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            {Platform.OS !== 'web' && (
              <TouchableOpacity
                style={[
                  styles.voiceButton,
                  isRecordingDescription && styles.voiceButtonRecording
                ]}
                onPress={() => {
                  if (isRecordingDescription) {
                    stopRecording();
                  } else {
                    startRecording('description');
                  }
                }}
              >
                {isRecordingDescription ? (
                  <MicOff size={20} color={colors.error} />
                ) : (
                  <Mic size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            )}
          </View>
          {isRecordingDescription && (
            <View style={styles.recordingIndicator}>
              <Text style={styles.recordingText}>{t('common.recording')}</Text>
            </View>
          )}
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('farms.farm')}*</Text>
          <GenericDropdown
            placeholder={t('farms.selectFarm')}
            items={farms.map(farm => ({
              id: farm.id,
              label: farm.name,
              icon: <MapPin size={20} color={colors.primary}/>
            }))}
            value={farmId}
            onSelect={handleFarmSelect}
            modalTitle={t('farms.selectFarm')}
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('tasks.assignTo')}*</Text>
          {loadingEmployees ? (
            <ActivityIndicator size="small" color={colors.primary}/>
          ) : (
            <GenericDropdown
              placeholder={t('tasks.selectAssignee')}
              items={employees.map(emp => ({
                id: emp.id,
                label: emp.name,
                imageUri: emp.photo,
                customItem: (
                  <View style={styles.employeeItem}>
                    {emp.photo ? (
                      <Image 
                        source={{ uri: emp.photo }} 
                        style={styles.employeePhoto}
                      />
                    ) : (
                      <View style={styles.employeePhotoPlaceholder}>
                        <Text style={styles.employeePhotoPlaceholderText}>
                          {emp.name.substring(0, 1).toUpperCase()}
                        </Text>
                      </View>
                    )}
                    <Text style={styles.employeeName}>{emp.name}</Text>
                  </View>
                )
              }))}
              value={assigneeId}
              onSelect={handleAssigneeSelect}
              modalTitle={t('tasks.selectAssignee')}
              disabled={loadingEmployees || employees.length === 0}
            />
          )}
          {!farmId && (
            <Text style={styles.helperText}>{t('tasks.selectFarmFirst')}</Text>
          )}
          {farmId && employees.length === 0 && !loadingEmployees && (
            <Text style={styles.helperText}>{t('tasks.noEmployeesInFarm')}</Text>
          )}
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('tasks.dueDate')}*</Text>
          <TouchableOpacity 
            style={styles.datePickerButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Calendar size={20} color={colors.primary}/>
            <Text style={styles.dateText}>{dueDate.toLocaleDateString()}</Text>
          </TouchableOpacity>
          
          {showDatePicker && (
            <DateTimePicker
              value={dueDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('tasks.priority')}</Text>
          <GenericDropdown
            placeholder={t('tasks.selectPriorityPlaceholder')}
            searchPlaceholder={t('tasks.searchPriorities')}
            items={priorityOptions}
            value={priority}
            onSelect={handlePrioritySelect}
            modalTitle={t('tasks.selectPriority')}
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('tasks.recurrences')}</Text>
          <GenericDropdown
            placeholder={t('tasks.selectRecurrencePlaceholder')}
            searchPlaceholder={t('tasks.searchRecurrences')}
            items={recurrenceOptions}
            value={recurrence}
            onSelect={handleRecurrenceSelect}
            modalTitle={t('tasks.selectRecurrence')}
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('tasks.notes')}</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={[
                styles.input, 
                styles.textArea, 
                styles.inputWithButton,
                isRecordingNotes && styles.inputRecording
              ]}
              value={notes}
              onChangeText={setNotes}
              placeholder={t('tasks.notesPlaceholder')}
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            {Platform.OS !== 'web' && (
              <TouchableOpacity
                style={[
                  styles.voiceButton,
                  isRecordingNotes && styles.voiceButtonRecording
                ]}
                onPress={() => {
                  if (isRecordingNotes) {
                    stopRecording();
                  } else {
                    startRecording('notes');
                  }
                }}
              >
                {isRecordingNotes ? (
                  <MicOff size={20} color={colors.error} />
                ) : (
                  <Mic size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            )}
          </View>
          {isRecordingNotes && (
            <View style={styles.recordingIndicator}>
              <Text style={styles.recordingText}>{t('common.recording')}</Text>
            </View>
          )}
        </View>
        
       {user?.role !== 'caretaker' && (
          <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="white"/>
          ) : (
            <>
            <Save size={20} color="white" />
            <Text style={styles.submitButtonText}>{t('common.save')}</Text>
          </>
          )}
        </TouchableOpacity>)}
      </ScrollView>
    </SafeAreaView>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background
  },
  scrollView: {
    flex: 1,
    padding: 16
  },
  formGroup: {
    marginBottom: 20
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 8
  },
  input: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: themedColors.text,
    borderWidth: 1,
    borderColor: themedColors.border
  },
  textArea: {
    minHeight: 100
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.card,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: themedColors.border
  },
  dateText: {
    fontSize: 16,
    color: themedColors.text,
    marginLeft: 8
  },
  helperText: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginTop: 4
  },
  submitButton: {
    backgroundColor: themedColors.primary,
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row', // Added to align icon and text
    justifyContent: 'center', // Added to center the icon and text
    gap:6,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: '600'
  },
  inputContainer: {
    position: 'relative',
    width: '100%'
  },
  inputWithButton: {
    paddingRight: 44
  },
  voiceButton: {
    position: 'absolute',
    right: 8,
    top: 8,
    padding: 8,
    borderRadius: 20,
    backgroundColor: themedColors.card,
    borderWidth: 1,
    borderColor: themedColors.border
  },
  employeeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8
  },
  employeePhoto: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12
  },
  employeePhotoPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: themedColors.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12
  },
  employeePhotoPlaceholderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: themedColors.primary
  },
  employeeName: {
    fontSize: 16,
    color: themedColors.text,
    marginLeft: 8
  },
  recordingIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center'
  },
  recordingText: {
    color: 'white',
    fontSize: 14
  },
  inputRecording: {
    backgroundColor: themedColors.card + '80',
    borderColor: themedColors.error,
    borderWidth: 1,
  },
  voiceButtonRecording: {
    backgroundColor: themedColors.error,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  recordingText: {
    color: colors.error,
    fontSize: 12,
    fontWeight: '500',
  },
});

export default AddTaskScreen;
