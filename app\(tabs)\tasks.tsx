import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { useRouter, Stack, useFocusEffect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Calendar, Users, ClipboardList, Activity as ActivityIcon } from 'lucide-react-native';
import { ErrorBoundary } from '@/app/error-boundary';

import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuthStore } from '@/store/auth-store';
import { useTaskStore } from '@/store/task-store';
import { TaskStatus } from '@/types/task';
import { getEmployeesByOwner } from '@/services/employee-service';
import { Employee } from '@/types/employee';
import { getActivities, Activity as ActivityType } from '@/services/activity-service';
import LoadingIndicator from '@/components/LoadingIndicator';
import CountCard from '@/components/CountCard';
import { TasksTab, StaffTab, RecentActivitiesTab } from '@/components/TaskTabs';

export default function TasksScreen() {
  return (
    <ErrorBoundary onError={(error, errorInfo) => {
      console.error("Tasks Screen Error:", error);
      console.error("Component Stack:", errorInfo.componentStack);
    }}>
      <TasksScreenContent />
    </ErrorBoundary>
  );
}

function TasksScreenContent() {
  const { t } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { tasks, loading, fetchTasks } = useTaskStore();
  const themedColors = useThemeColors();

  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'tasks' | 'staff' | 'activities'>('tasks');
  const [filter, setFilter] = useState<'all' | 'pending' | 'completed'>('all');
  const [staff, setStaff] = useState<Employee[]>([]);
  const [loadingStaff, setLoadingStaff] = useState(false);
  const [activities, setActivities] = useState<ActivityType[]>([]);
  const [loadingActivities, setLoadingActivities] = useState(false);

  const styles = getStyles(themedColors);

  // Use useCallback to memoize the loadTasks function
  const loadTasks = useCallback(async () => {
    if (!user) {
      return;
    }

    try {
      await fetchTasks(user.id, user.role);
    } catch (error) {
      console.error('Error in loadTasks:', error);
    }
  }, [user, fetchTasks]);

  // Safe filtering function
  const getFilteredTasks = useCallback(() => {
    if (!tasks || !Array.isArray(tasks)) {
      return [];
    }

    return tasks.filter(task => {
      if (!task) {
        return false;
      }

      try {
        if (filter === 'all') return true;
        if (filter === 'pending') return task.status !== TaskStatus.COMPLETED;
        if (filter === 'completed') return task.status === TaskStatus.COMPLETED;
        return true;
      } catch (err) {
        console.error('Error filtering task:', err, task);
        return false;
      }
    });
  }, [tasks, filter]);

  useEffect(() => {
    if (user) {
      // Load tasks, staff, and activities data when component mounts
      loadTasks();
      loadStaff();
      loadActivities();
    }
  }, [user]);

  // Separate effect for tab changes
  useEffect(() => {
    if (user) {
      if (activeTab === 'tasks') {
        loadTasks();
      } else if (activeTab === 'staff') {
        loadStaff();
      } else if (activeTab === 'activities') {
        loadActivities();
      }
    }
  }, [user, activeTab]);

  // Refresh data when screen comes back into focus
  useFocusEffect(
    useCallback(() => {
      if (user) {
        if (activeTab === 'staff') {
          loadStaff();
        } else if (activeTab === 'activities') {
          loadActivities();
        }
      }
    }, [user, activeTab])
  );

  const loadStaff = async () => {
    if (!user) return;

    setLoadingStaff(true);
    try {

      // Fetch staff based on user role
      if (user.role === 'owner' || user.role === 'admin') {
        // Owners and admins can see all staff
        const staffMembers = await getEmployeesByOwner(user.id);

        // If user is admin, filter out other admins and owners
        if (user.role === 'admin') {
          const filteredStaff = staffMembers.filter(
            (member: any) => member.role === 'caretaker'
          );
          setStaff(filteredStaff as Employee[]);
        } else {
          // Owner can see all staff (admins and caretakers)
          setStaff(staffMembers as Employee[]);
        }

      } else {
        // Caretakers shouldn't see this tab, but just in case
        setStaff([]);
      }
    } catch (error) {
      console.error('Error loading staff:', error);
    } finally {
      setLoadingStaff(false);
    }
  };

  const loadActivities = async () => {
    if (!user) return;

    setLoadingActivities(true);
    try {
      const activitiesData = await getActivities(user.id, user.role);
      setActivities(activitiesData);
    } catch (error) {
      console.error('Error loading activities:', error);
    } finally {
      setLoadingActivities(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    if (activeTab === 'tasks') {
      await loadTasks();
    } else if (activeTab === 'staff') {
      await loadStaff();
    } else if (activeTab === 'activities') {
      await loadActivities();
    }
    setRefreshing(false);
  };

  const handleAddTask = () => {
    router.push('/tasks/add');
  };

  const handleAddStaff = () => {
    router.push('/employees/add');
  };

  const handleTaskPress = (taskId: string) => {
    router.push(`/tasks/${taskId}`);
  };

  // Removed unused handleAssignStaff function

  if (activeTab === 'tasks' && loading && !refreshing) {
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors?.background }]} edges={['left', 'right']}>
     <LoadingIndicator fullScreen message={t('tasks.loading')} />
     </SafeAreaView>
  } else if (activeTab === 'staff' && loadingStaff && !refreshing) {
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors?.background }]} edges={['left', 'right']}>
     <LoadingIndicator fullScreen message={t('staff.loading')} />
     </SafeAreaView>
  } else if (activeTab === 'activities' && loadingActivities && !refreshing) {
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors?.background }]} edges={['left', 'right']}>
     <LoadingIndicator fullScreen message={t('activities.loading')} />
     </SafeAreaView>
  }


  const renderCountCards = () => {
    // Count tasks
    const taskCount = tasks?.length || 0;

    // Count staff
    const staffCount = staff?.length || 0;

    // Count activities
    const activitiesCount = activities?.length || 0;

    return (
      <View style={styles.countCardsContainer}>
        <CountCard
          count={taskCount}
          label={t('tasks.tasks')}
          icon={<ClipboardList size={24} color={themedColors.primary} />}
          onPress={() => setActiveTab('tasks')}
          isActive={activeTab === 'tasks'}
        />
       {user?.role !== 'caretaker' && <CountCard
          count={staffCount}
          label={t('staff.staff')}
          icon={<Users size={24} color={themedColors.primary} />}
          onPress={() => setActiveTab('staff')}
          isActive={activeTab === 'staff'}
        />}
       {user?.role === 'caretaker' && <CountCard
          count={activitiesCount}
          label={t('activities.recentActivities')}
          icon={<ActivityIcon size={24} color={themedColors.primary} />}
          onPress={() => setActiveTab('activities')}
          isActive={activeTab === 'activities'}
        />}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom', 'left', 'right']}>
      <Stack.Screen
        options={{
          title: t('tasks.title'),
          headerBackTitle: t('common.back')
        }}
      />

      {/* Count Cards */}
      {renderCountCards()}

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'tasks' && styles.activeTabButton]}
          onPress={() => setActiveTab('tasks')}
        >
          <Calendar size={14} color={activeTab === 'tasks' ? themedColors.primary : themedColors.textSecondary} />
          <Text style={[styles.tabButtonText, activeTab === 'tasks' && styles.activeTabText]}>
            {t('tasks.tasks')}
          </Text>
        </TouchableOpacity>

        {
          user?.role !== 'caretaker' && <TouchableOpacity
            style={[styles.tabButton, activeTab === 'staff' && styles.activeTabButton]}
            onPress={() => setActiveTab('staff')}
          >
            <Users size={14} color={activeTab === 'staff' ? themedColors.primary : themedColors.textSecondary} />
            <Text style={[styles.tabButtonText, activeTab === 'staff' && styles.activeTabText]}>
              {t('staff.assignStaff')}
            </Text>
          </TouchableOpacity>
        }

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'activities' && styles.activeTabButton]}
          onPress={() => setActiveTab('activities')}
        >
          <ActivityIcon size={14} color={activeTab === 'activities' ? themedColors.primary : themedColors.textSecondary} />
          <Text
            style={[styles.tabButtonText, activeTab === 'activities' && styles.activeTabText]}
            numberOfLines={1}
            adjustsFontSizeToFit={true}
            minimumFontScale={0.8}
          >
            {t('activities.title')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      <View style={styles.tabContentContainer}>
        {activeTab === 'tasks' && (
          <TasksTab
            tasks={tasks || []}
            filter={filter}
            onFilterChange={setFilter}
            onTaskPress={handleTaskPress}
            onAddTask={handleAddTask}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        )}
        {activeTab === 'staff' && user?.role !== 'caretaker' && (
          <StaffTab
            staff={staff}
            loadingStaff={loadingStaff}
            userRole={user?.role}
            onAddStaff={handleAddStaff}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        )}
        {activeTab === 'activities' && (
          <RecentActivitiesTab
            activities={activities}
            loading={loadingActivities}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        )}
      </View>

      {/* Add Button */}
     {user?.role !== 'caretaker' && activeTab !== 'activities' && ( <TouchableOpacity
        style={styles.addButton}
        onPress={activeTab === 'tasks' ? handleAddTask : handleAddStaff}
      >
        <Plus size={24} color={themedColors.isDarkMode ? themedColors.textContrast : 'white'} />
      </TouchableOpacity>)}
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  countCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: themedColors.background,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
    paddingHorizontal: 2,
    justifyContent: 'space-between', // This ensures equal spacing
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 6,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    flex: 1, // Make each tab take equal space
    justifyContent: 'center', // Center the content horizontally
  },
  activeTabButton: {
    borderBottomColor: themedColors.primary,
  },
  tabButtonText: {
    marginLeft: 2,
    fontSize: 14,
    fontWeight: '500',
    color: themedColors.textSecondary,
  },
  activeTabText: {
    color: themedColors.primary,
    fontWeight: 'bold',
  },
  tabContentContainer: {
    flex: 1,
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: themedColors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});
