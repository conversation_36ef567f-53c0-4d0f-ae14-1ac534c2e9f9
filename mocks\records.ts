import { AnimalRecord } from '@/types/animal';

export const animalRecords: AnimalRecord[] = [
  {
    id: '1',
    animalId: '1',
    date: Date.now() - 1000 * 60 * 60 * 24 * 30, // 30 days ago
    symptoms: ['fever', 'cough', 'lethargy'],
    diagnosis: 'Bovine Respiratory Disease',
    treatment: 'Antibiotics for 7 days, rest and fluids',
    notes: 'Animal responded well to treatment after 3 days',
    predictedDiseases: [
      {
        id: 'brd1',
        name: 'Bovine Respiratory Disease',
        description: 'Common respiratory infection in cattle',
        symptoms: ['fever', 'cough', 'nasal discharge', 'lethargy'],
        treatments: ['antibiotics', 'anti-inflammatories', 'rest'],
        severity: 'medium',
        zoonotic: false
      }
    ],
    confirmedDisease: {
      id: 'brd1',
      name: 'Bovine Respiratory Disease',
      description: 'Common respiratory infection in cattle',
      symptoms: ['fever', 'cough', 'nasal discharge', 'lethargy'],
      treatments: ['antibiotics', 'anti-inflammatories', 'rest'],
      severity: 'medium',
      zoonotic: false
    },
    createdBy: 'user1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 30,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 27,
  },
  {
    id: '2',
    animalId: '2',
    date: Date.now() - 1000 * 60 * 60 * 24 * 15, // 15 days ago
    symptoms: ['vomiting', 'diarrhea', 'loss of appetite'],
    diagnosis: 'Canine Parvovirus',
    treatment: 'IV fluids, antibiotics, anti-nausea medication',
    notes: 'Severe case, hospitalization required',
    predictedDiseases: [
      {
        id: 'parvo1',
        name: 'Canine Parvovirus',
        description: 'Highly contagious viral illness affecting dogs',
        symptoms: ['vomiting', 'diarrhea', 'lethargy', 'loss of appetite', 'fever'],
        treatments: ['supportive care', 'fluid therapy', 'antibiotics'],
        severity: 'high',
        zoonotic: false
      }
    ],
    confirmedDisease: {
      id: 'parvo1',
      name: 'Canine Parvovirus',
      description: 'Highly contagious viral illness affecting dogs',
      symptoms: ['vomiting', 'diarrhea', 'lethargy', 'loss of appetite', 'fever'],
      treatments: ['supportive care', 'fluid therapy', 'antibiotics'],
      severity: 'high',
      zoonotic: false
    },
    createdBy: 'healthcaretaker1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 15,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 12,
  },
  {
    id: '3',
    animalId: '3',
    date: Date.now() - 1000 * 60 * 60 * 24 * 7, // 7 days ago
    symptoms: ['limping', 'swollen joint', 'reduced mobility'],
    diagnosis: 'Caprine Arthritis Encephalitis',
    treatment: 'Anti-inflammatories, joint supplements, management of environment',
    notes: 'Chronic condition, will need ongoing management',
    predictedDiseases: [
      {
        id: 'cae1',
        name: 'Caprine Arthritis Encephalitis',
        description: 'Viral disease affecting goats causing arthritis and other issues',
        symptoms: ['swollen joints', 'limping', 'weight loss', 'hard udder'],
        treatments: ['supportive care', 'anti-inflammatories', 'management'],
        severity: 'medium',
        zoonotic: false
      }
    ],
    confirmedDisease: {
      id: 'cae1',
      name: 'Caprine Arthritis Encephalitis',
      description: 'Viral disease affecting goats causing arthritis and other issues',
      symptoms: ['swollen joints', 'limping', 'weight loss', 'hard udder'],
      treatments: ['supportive care', 'anti-inflammatories', 'management'],
      severity: 'medium',
      zoonotic: false
    },
    createdBy: 'user1',
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 7,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 5,
  },
];