import { User } from '@/types/user';

export const users: User[] = [
  {
    id: 'user1',
    email: '<EMAIL>',
    phone: '+**********',
    name: '<PERSON>',
    role: 'owner',
    language: 'en',
    preferAudio: false,
    offlineMode: false,
    lastSyncTime: Date.now() - 1000 * 60 * 60, // 1 hour ago
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 100, // 100 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 24, // 1 day ago
  },
  {
    id: 'healthcaretaker1',
    email: '<EMAIL>',
    phone: '+**********',
    name: 'Dr. <PERSON>',
    role: 'caretaker',
    language: 'en',
    preferAudio: false,
    offlineMode: false,
    lastSyncTime: Date.now() - 1000 * 60 * 30, // 30 minutes ago
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 200, // 200 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 12, // 12 hours ago
  },
  {
    id: 'admin1',
    email: '<EMAIL>',
    phone: '+**********',
    name: 'Admin User',
    role: 'admin',
    language: 'en',
    preferAudio: false,
    offlineMode: false,
    lastSyncTime: Date.now() - 1000 * 60 * 10, // 10 minutes ago
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 300, // 300 days ago
    updatedAt: Date.now() - 1000 * 60 * 60 * 2, // 2 hours ago
  },
];