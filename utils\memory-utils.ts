/**
 * Memory utilities for debugging and optimization
 */

export const logMemoryUsage = (context: string) => {
  if (__DEV__) {
    // In development, log memory usage context
    console.log(`[Memory] ${context} - Timestamp: ${new Date().toISOString()}`);
    
    // For React Native, we can't directly access memory usage
    // but we can log when heavy operations occur
    if (global.performance && global.performance.memory) {
      const memory = global.performance.memory;
      console.log(`[Memory] ${context}:`, {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
      });
    }
  }
};

export const createMemoryOptimizedTimeout = (callback: () => void, delay: number = 100) => {
  return setTimeout(() => {
    try {
      callback();
    } catch (error) {
      console.error('[Memory] Error in optimized timeout:', error);
    }
  }, delay);
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      try {
        func(...args);
      } catch (error) {
        console.error('[Memory] Error in debounced function:', error);
      }
    }, wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      try {
        func(...args);
      } catch (error) {
        console.error('[Memory] Error in throttled function:', error);
      }
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Helper to safely handle large data arrays
export const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

// Helper to limit array size for memory optimization
export const limitArraySize = <T>(array: T[], maxSize: number): T[] => {
  if (array.length <= maxSize) {
    return array;
  }
  
  console.warn(`[Memory] Array size (${array.length}) exceeds limit (${maxSize}), truncating`);
  return array.slice(0, maxSize);
};
