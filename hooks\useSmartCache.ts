/**
 * Smart caching hook for React components
 * Provides intelligent data fetching with cache management
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { getCacheStats, cleanExpiredCache } from '@/utils/firebase-optimization';
import { startPerformanceTimer, endPerformanceTimer } from '@/utils/memory-utils';

interface CacheOptions {
  // Cache key for the data
  key: string;
  // Function to fetch fresh data
  fetchFn: () => Promise<any>;
  // Cache TTL in milliseconds (default: 5 minutes)
  ttl?: number;
  // Whether to fetch on component focus
  refreshOnFocus?: boolean;
  // Whether to show loading state on refresh
  showLoadingOnRefresh?: boolean;
  // Dependencies that trigger cache invalidation
  dependencies?: any[];
  // Whether to fetch data immediately on mount
  fetchOnMount?: boolean;
}

interface CacheState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
  isStale: boolean;
}

// In-memory cache for component-level data
const componentCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

export function useSmartCache<T = any>({
  key,
  fetchFn,
  ttl = 5 * 60 * 1000, // 5 minutes default
  refreshOnFocus = true,
  showLoadingOnRefresh = false,
  dependencies = [],
  fetchOnMount = true
}: CacheOptions) {
  const [state, setState] = useState<CacheState<T>>({
    data: null,
    isLoading: false,
    error: null,
    lastFetched: null,
    isStale: false
  });

  const isMountedRef = useRef(true);
  const lastDependenciesRef = useRef(dependencies);

  // Check if cache is valid
  const isCacheValid = useCallback((cacheEntry: { timestamp: number; ttl: number }) => {
    return Date.now() - cacheEntry.timestamp < cacheEntry.ttl;
  }, []);

  // Get cached data
  const getCachedData = useCallback(() => {
    const cached = componentCache.get(key);
    if (cached && isCacheValid(cached)) {
      return cached.data;
    }
    return null;
  }, [key, isCacheValid]);

  // Set cached data
  const setCachedData = useCallback((data: T) => {
    componentCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }, [key, ttl]);

  // Check if dependencies have changed
  const dependenciesChanged = useCallback(() => {
    if (lastDependenciesRef.current.length !== dependencies.length) {
      return true;
    }
    return lastDependenciesRef.current.some((dep, index) => dep !== dependencies[index]);
  }, [dependencies]);

  // Fetch data function
  const fetchData = useCallback(async (showLoading = true) => {
    if (!isMountedRef.current) return;

    // Check cache first
    const cachedData = getCachedData();
    if (cachedData && !dependenciesChanged()) {
      setState(prev => ({
        ...prev,
        data: cachedData,
        isLoading: false,
        error: null,
        lastFetched: Date.now(),
        isStale: false
      }));
      return cachedData;
    }

    if (showLoading) {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
    }

    const performanceLabel = `SmartCache_${key}`;
    startPerformanceTimer(performanceLabel);

    try {
      const data = await fetchFn();
      
      if (!isMountedRef.current) return;

      // Cache the data
      setCachedData(data);
      
      setState({
        data,
        isLoading: false,
        error: null,
        lastFetched: Date.now(),
        isStale: false
      });

      // Update dependencies reference
      lastDependenciesRef.current = [...dependencies];

      endPerformanceTimer(performanceLabel);
      console.log(`[SmartCache] Data fetched and cached for key: ${key}`);
      
      return data;
    } catch (error) {
      if (!isMountedRef.current) return;

      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch data';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
        isStale: true
      }));

      endPerformanceTimer(performanceLabel);
      console.error(`[SmartCache] Error fetching data for key ${key}:`, error);
      throw error;
    }
  }, [key, fetchFn, getCachedData, setCachedData, dependencies, dependenciesChanged]);

  // Refresh data manually
  const refresh = useCallback(async () => {
    return fetchData(showLoadingOnRefresh);
  }, [fetchData, showLoadingOnRefresh]);

  // Invalidate cache for this key
  const invalidate = useCallback(() => {
    componentCache.delete(key);
    setState(prev => ({ ...prev, isStale: true }));
  }, [key]);

  // Check if data is stale
  const checkStale = useCallback(() => {
    const cached = componentCache.get(key);
    if (!cached) {
      setState(prev => ({ ...prev, isStale: true }));
      return true;
    }

    const isStale = !isCacheValid(cached);
    setState(prev => ({ ...prev, isStale }));
    return isStale;
  }, [key, isCacheValid]);

  // Initial fetch on mount
  useEffect(() => {
    if (fetchOnMount) {
      fetchData();
    }
  }, []); // Only run on mount

  // Fetch when dependencies change
  useEffect(() => {
    if (dependenciesChanged() && state.data !== null) {
      console.log(`[SmartCache] Dependencies changed for key: ${key}, refetching...`);
      fetchData();
    }
  }, dependencies);

  // Refresh on focus if enabled
  useFocusEffect(
    useCallback(() => {
      if (refreshOnFocus && state.data !== null) {
        const isStale = checkStale();
        if (isStale) {
          console.log(`[SmartCache] Data is stale for key: ${key}, refreshing on focus...`);
          fetchData(false); // Don't show loading on focus refresh
        }
      }
    }, [refreshOnFocus, state.data, checkStale, fetchData, key])
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    ...state,
    refresh,
    invalidate,
    checkStale,
    // Utility functions
    getCacheStats: () => getCacheStats(),
    cleanExpiredCache: () => cleanExpiredCache()
  };
}

// Hook for managing multiple cache keys
export function useMultiCache<T = any>(cacheConfigs: CacheOptions[]) {
  const cacheResults = cacheConfigs.map(config => useSmartCache<T>(config));

  const refreshAll = useCallback(async () => {
    const promises = cacheResults.map(result => result.refresh());
    return Promise.all(promises);
  }, [cacheResults]);

  const invalidateAll = useCallback(() => {
    cacheResults.forEach(result => result.invalidate());
  }, [cacheResults]);

  const isAnyLoading = cacheResults.some(result => result.isLoading);
  const hasAnyError = cacheResults.some(result => result.error);
  const allData = cacheResults.map(result => result.data);

  return {
    results: cacheResults,
    refreshAll,
    invalidateAll,
    isAnyLoading,
    hasAnyError,
    allData
  };
}

// Global cache management
export const CacheManager = {
  // Clear all component cache
  clearAll: () => {
    const size = componentCache.size;
    componentCache.clear();
    console.log(`[CacheManager] Cleared all component cache: ${size} entries`);
  },

  // Clear cache by pattern
  clearByPattern: (pattern: string) => {
    const keysToDelete: string[] = [];
    componentCache.forEach((_, key) => {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => componentCache.delete(key));
    console.log(`[CacheManager] Cleared cache by pattern "${pattern}": ${keysToDelete.length} entries`);
  },

  // Get cache statistics
  getStats: () => {
    const stats = {
      totalEntries: componentCache.size,
      validEntries: 0,
      expiredEntries: 0
    };

    const now = Date.now();
    componentCache.forEach((entry) => {
      if (now - entry.timestamp < entry.ttl) {
        stats.validEntries++;
      } else {
        stats.expiredEntries++;
      }
    });

    return stats;
  },

  // Clean expired entries
  cleanExpired: () => {
    const keysToDelete: string[] = [];
    const now = Date.now();
    
    componentCache.forEach((entry, key) => {
      if (now - entry.timestamp >= entry.ttl) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => componentCache.delete(key));
    console.log(`[CacheManager] Cleaned expired component cache entries: ${keysToDelete.length}`);
    
    return keysToDelete.length;
  }
};
