import { addActivity } from '@/services/activity-service';

export const addSampleActivities = async (farmId: string, userId: string) => {
  const sampleActivities = [
    {
      title: "Animal Health Check Completed",
      description: "Completed routine health check for cow #123. All vitals normal.",
      createdBy: userId,
      updatedBy: userId,
      appType: "2",
      farmId: farmId
    },
    {
      title: "New Animal Added",
      description: "Added new buffalo to the farm. Tag number: BUF-456",
      createdBy: userId,
      updatedBy: userId,
      appType: "2",
      farmId: farmId
    },
    {
      title: "Vaccination Administered",
      description: "Administered FMD vaccine to 5 cattle. Next vaccination due in 6 months.",
      createdBy: userId,
      updatedBy: userId,
      appType: "2",
      farmId: farmId
    },
    {
      title: "Feed Purchase",
      description: "Purchased 100kg of cattle feed. Total cost: Rs. 5,000",
      createdBy: userId,
      updatedBy: userId,
      appType: "2",
      farmId: farmId
    },
    {
      title: "Milking Record Updated",
      description: "Morning milking completed. Total yield: 45 liters from 8 animals.",
      createdBy: userId,
      updatedBy: userId,
      appType: "2",
      farmId: farmId
    }
  ];

  try {
    for (const activity of sampleActivities) {
      await addActivity(activity);
      // Add small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    console.log('Sample activities added successfully');
  } catch (error) {
    console.error('Error adding sample activities:', error);
  }
};
