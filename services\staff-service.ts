import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';

export interface StaffMember {
  id: string;
  name: string;
  email: string;
  phone?: string;
  phoneNumber?: string;
  role: 'owner' | 'admin' | 'caretaker';
  status?: 'active' | 'inactive';
  photoURL?: string;
  assignedFarmIds?: string[];
  createdAt?: any;
  updatedAt?: any;
  isEmployee?: boolean;
}

/**
 * Get all staff members assigned to a specific farm
 * @param farmId - The ID of the farm
 * @returns Array of staff members assigned to the farm
 */
export const getStaffByFarmId = async (farmId: string): Promise<StaffMember[]> => {
  try {
    const usersRef = collection(firestore, 'users');
    const q = query(
      usersRef,
      where('assignedFarmIds', 'array-contains', farmId)
    );
    
    const querySnapshot = await getDocs(q);
    const staff: StaffMember[] = [];
    
    querySnapshot.forEach((doc) => {
      const userData = doc.data();
      staff.push({
        id: doc.id,
        ...userData,
      } as StaffMember);
    });
    
    return staff;
  } catch (error) {
    console.error('Error fetching staff by farm ID:', error);
    throw error;
  }
};

/**
 * Get the count of staff members assigned to a specific farm
 * @param farmId - The ID of the farm
 * @returns Number of staff members assigned to the farm
 */
export const getStaffCountByFarmId = async (farmId: string): Promise<number> => {
  try {
    const staff = await getStaffByFarmId(farmId);
    return staff.length;
  } catch (error) {
    console.error('Error getting staff count by farm ID:', error);
    return 0;
  }
};

/**
 * Get staff counts for multiple farms at once
 * @param farmIds - Array of farm IDs
 * @returns Object with farmId as key and staff count as value
 */
export const getStaffCountsForFarms = async (farmIds: string[]): Promise<Record<string, number>> => {
  try {
    if (farmIds.length === 0) return {};
    
    const usersRef = collection(firestore, 'users');
    const q = query(usersRef);
    
    const querySnapshot = await getDocs(q);
    const staffCounts: Record<string, number> = {};
    
    // Initialize all farm counts to 0
    farmIds.forEach(farmId => {
      staffCounts[farmId] = 0;
    });
    
    // Count staff for each farm
    querySnapshot.forEach((doc) => {
      const userData = doc.data();
      const assignedFarmIds = userData.assignedFarmIds || [];
      
      // For each farm this user is assigned to, increment the count
      assignedFarmIds.forEach((farmId: string) => {
        if (farmIds.includes(farmId)) {
          staffCounts[farmId] = (staffCounts[farmId] || 0) + 1;
        }
      });
    });
    
    return staffCounts;
  } catch (error) {
    console.error('Error getting staff counts for farms:', error);
    return {};
  }
};

/**
 * Get a single staff member by ID
 * @param staffId - The ID of the staff member
 * @returns Staff member data or null if not found
 */
export const getStaffById = async (staffId: string): Promise<StaffMember | null> => {
  try {
    const userRef = doc(firestore, 'users', staffId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      return {
        id: userDoc.id,
        ...userDoc.data(),
      } as StaffMember;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching staff by ID:', error);
    throw error;
  }
};

/**
 * Get all staff members for farms owned by a specific user
 * @param ownerId - The ID of the farm owner
 * @returns Array of staff members across all owned farms
 */
export const getStaffByOwnerId = async (ownerId: string): Promise<StaffMember[]> => {
  try {
    // First get all farms owned by this user
    const farmsRef = collection(firestore, 'farms');
    const farmsQuery = query(farmsRef, where('ownerId', '==', ownerId));
    const farmsSnapshot = await getDocs(farmsQuery);
    
    const farmIds: string[] = [];
    farmsSnapshot.forEach((doc) => {
      farmIds.push(doc.id);
    });
    
    if (farmIds.length === 0) return [];
    
    // Then get all staff assigned to any of these farms
    const usersRef = collection(firestore, 'users');
    const staffSet = new Set<string>(); // Use Set to avoid duplicates
    const staff: StaffMember[] = [];
    
    // Query for each farm ID (Firebase doesn't support array-contains-any with large arrays efficiently)
    for (const farmId of farmIds) {
      const q = query(
        usersRef,
        where('assignedFarmIds', 'array-contains', farmId)
      );
      
      const querySnapshot = await getDocs(q);
      querySnapshot.forEach((doc) => {
        if (!staffSet.has(doc.id)) {
          staffSet.add(doc.id);
          const userData = doc.data();
          staff.push({
            id: doc.id,
            ...userData,
          } as StaffMember);
        }
      });
    }
    
    return staff;
  } catch (error) {
    console.error('Error fetching staff by owner ID:', error);
    throw error;
  }
};
