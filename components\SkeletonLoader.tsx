/**
 * Skeleton loader components for better loading experience
 */

import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}

export const SkeletonBox: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style
}) => {
  const themedColors = useThemeColors();
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: false,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [themedColors.cardBackground, themedColors.border],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

export const SkeletonCard: React.FC = () => {
  const themedColors = useThemeColors();
  const styles = getSkeletonStyles(themedColors);

  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <SkeletonBox width={60} height={60} borderRadius={30} />
        <View style={styles.cardHeaderText}>
          <SkeletonBox width="70%" height={16} />
          <SkeletonBox width="50%" height={12} style={{ marginTop: 8 }} />
        </View>
      </View>
      <View style={styles.cardContent}>
        <SkeletonBox width="100%" height={12} />
        <SkeletonBox width="80%" height={12} style={{ marginTop: 6 }} />
        <SkeletonBox width="60%" height={12} style={{ marginTop: 6 }} />
      </View>
      <View style={styles.cardFooter}>
        <SkeletonBox width={80} height={32} borderRadius={16} />
        <SkeletonBox width={80} height={32} borderRadius={16} />
      </View>
    </View>
  );
};

export const SkeletonList: React.FC<{ count?: number }> = ({ count = 3 }) => {
  return (
    <View>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </View>
  );
};

export const SkeletonStats: React.FC = () => {
  const themedColors = useThemeColors();
  const styles = getSkeletonStyles(themedColors);

  return (
    <View style={styles.statsContainer}>
      {Array.from({ length: 4 }).map((_, index) => (
        <View key={index} style={styles.statCard}>
          <SkeletonBox width={40} height={40} borderRadius={20} />
          <SkeletonBox width="60%" height={14} style={{ marginTop: 8 }} />
          <SkeletonBox width="40%" height={12} style={{ marginTop: 4 }} />
        </View>
      ))}
    </View>
  );
};

export const SkeletonDashboard: React.FC = () => {
  const themedColors = useThemeColors();
  const styles = getSkeletonStyles(themedColors);

  return (
    <View style={styles.container}>
      {/* Header skeleton */}
      <View style={styles.header}>
        <View>
          <SkeletonBox width="60%" height={24} />
          <SkeletonBox width="80%" height={16} style={{ marginTop: 8 }} />
        </View>
        <SkeletonBox width={60} height={32} borderRadius={16} />
      </View>

      {/* Farm selector skeleton */}
      <View style={styles.farmSelector}>
        <SkeletonBox width="100%" height={48} borderRadius={8} />
      </View>

      {/* Stats skeleton */}
      <SkeletonStats />

      {/* Quick actions skeleton */}
      <View style={styles.quickActions}>
        {Array.from({ length: 3 }).map((_, index) => (
          <SkeletonBox key={index} width={100} height={80} borderRadius={12} />
        ))}
      </View>

      {/* Animals list skeleton */}
      <View style={styles.section}>
        <SkeletonBox width="40%" height={20} style={{ marginBottom: 16 }} />
        <SkeletonList count={2} />
      </View>
    </View>
  );
};

const getSkeletonStyles = (themedColors: any) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: themedColors.background,
  },
  card: {
    backgroundColor: themedColors.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: themedColors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardHeaderText: {
    flex: 1,
    marginLeft: 12,
  },
  cardContent: {
    marginBottom: 12,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  farmSelector: {
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: themedColors.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
});

export default SkeletonBox;
