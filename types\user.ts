export interface User {
  id: string;
  email: string;
  name: string;
  role: 'owner' | 'admin' | 'caretaker';
  language: string;
  preferAudio: boolean;
  offlineMode: boolean;
  emailVerified: boolean;
  profilePicture?: string; // URL to profile picture
  createdAt: number;
  updatedAt: number;

  // Multi-tenancy fields
  tenantId?: string; // For admin/caretaker roles, references the owner's ID
  assignedFarmIds?: string[]; // For all roles, list of farm IDs they have access to (owned for owners, assigned for admin/caretaker)
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Auth methods
  init: () => void;
  checkAuthState: () => Promise<any>;
  register: (email: string, password: string, name: string) => Promise<string>;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  sendEmailVerification: () => Promise<void>;
  verifyEmail: (oobCode?: string) => Promise<boolean>;
}