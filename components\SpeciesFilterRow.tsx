import React,{useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView } from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useLookupStore } from '@/store/lookup-store';

interface SpeciesOption {
  id: string;
  label: string;
  imageUrl: string;
}

interface SpeciesFilterRowProps {
  selectedSpecies: string | null;
  onSelectSpecies: (species: string | null) => void;
}

const SpeciesFilterRow: React.FC<SpeciesFilterRowProps> = ({
  selectedSpecies,
  onSelectSpecies,
}) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);
  const { getLookupsByCategory } = useLookupStore();

  const speciesOptions: SpeciesOption[] = useMemo(() => {
    const speciesLookups = getLookupsByCategory('animalSpecies');
    if (!speciesLookups || speciesLookups.length === 0) {
      return [];
    }
    return speciesLookups.map(lookup => ({
      id: lookup.title, // e.g., 'Cow'
      label: lookup.title,
      imageUrl: lookup.imageUrl || 'https://images.unsplash.com/photo-1546445317-29f4545e9d53?q=80&w=1000&auto=format&fit=crop' // Fallback image
    }));
  }, [getLookupsByCategory]);

  const renderSpeciesLabel = useCallback((species: SpeciesOption) => {
    if (!species || !species.id) return species?.label || '';
    try {
      const translationKey = `animals.${species.id.toLowerCase()}`;
      const translatedText = t(translationKey);
      return translatedText || species.label;
    } catch (error) {
      console.error('Error in species translation:', error);
      return species.label;
    }
  }, [t]);

  return (
    <View style={styles.container}>
      <View style={styles.scrollContent}>
        {speciesOptions.map((species) => (
          <TouchableOpacity
            key={species.id}
            style={[
              styles.speciesItem,
              selectedSpecies === species.id && styles.selectedSpeciesItem
            ]}
            onPress={() => onSelectSpecies(
              selectedSpecies === species.id ? null : species.id
            )}
          >
            <View style={[
              styles.imageContainer,
              selectedSpecies === species.id && styles.selectedImageContainer
            ]}>
              <Image
                source={{ uri: species.imageUrl }}
                style={styles.speciesImage}
                resizeMode="cover"
              />
            </View>
            <Text style={[
              styles.speciesText,
              selectedSpecies === species.id && styles.selectedSpeciesText,
              styles.languageSpecificText
            ]}>
              {renderSpeciesLabel(species)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  languageSpecificText: {
    ...(language === 'ur' && { fontFamily: 'System', textAlign: 'right' as 'right' }),
  },
  container: {
    marginBottom: 16,
    backgroundColor: themedColors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
    width: '100%',
  },
  scrollContent: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    padding: 8,
    width: '100%',
  },
  speciesItem: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 2,
  },
  selectedSpeciesItem: {
    opacity: 1,
  },
  imageContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: themedColors.border,
    marginBottom: 4,
  },
  selectedImageContainer: {
    borderColor: themedColors.primary,
    borderWidth: 3,
  },
  speciesImage: {
    width: '100%',
    height: '100%',
  },
  speciesText: {
    fontSize: 12,
    color: themedColors.text,
    textAlign: 'center',
  },
  selectedSpeciesText: {
    fontWeight: 'bold',
    color: themedColors.primary,
  },
});

export default SpeciesFilterRow;
