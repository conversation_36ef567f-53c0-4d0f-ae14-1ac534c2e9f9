import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import { useFocusEffect } from 'expo-router';
import { Milk, Calendar, TrendingUp, Award, Plus } from 'lucide-react-native';
import { LineChart } from 'react-native-chart-kit';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { useMilkingStore } from '@/store/milking-store';

import { Farm } from '@/types/farm';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';


interface MilkingTabProps {
  farm: Farm;
  onAddMilking: () => void;
  styles: any;
}

export default function MilkingTab({ farm, onAddMilking, styles }: MilkingTabProps) {
  const { t, language } = useTranslation();
  const router = useRouter();
  const themedColors = useThemeColors();
  const screenWidth = Dimensions.get('window').width;
  
  const {
    records,
    stats,
    isLoading,
    error,
    fetchRecordsByFarm,
    fetchStats,
    clearError,
  } = useMilkingStore();

  const [selectedPeriod, setSelectedPeriod] = useState(30); // days

  useEffect(() => {
    if (farm.id) {
      // Clear any previous errors
      clearError();

      // Add timeout to prevent memory issues
      const timeoutId = setTimeout(() => {
        try {
          fetchRecordsByFarm(farm.id);
          fetchStats(farm.id, selectedPeriod);
        } catch (error) {
          console.error('MilkingTab: Error fetching data', error);
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [farm.id, selectedPeriod]);

  // Refresh data when tab becomes active (e.g., after adding a record)
  useFocusEffect(
    React.useCallback(() => {
      if (farm.id) {
        fetchRecordsByFarm(farm.id);
        fetchStats(farm.id, selectedPeriod);
      }
    }, [farm.id, selectedPeriod])
  );



  const renderStatsCard = () => {
    if (!stats) return null;

    return (
      <View style={styles.milkingStatsContainer}>
        <View style={[styles.milkingStatsHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
          <Text style={[styles.milkingStatsTitle, language === 'ur' && styles.urduText]}>
            {t('milking.statistics')}
          </Text>
          <TouchableOpacity
            style={styles.milkingViewAllButton}
            onPress={() => router.push(`/milking/farm-records?farmId=${farm.id}`)}
          >
            <Text style={[styles.milkingViewAllText, language === 'ur' && styles.urduText]}>
              {t('common.viewAll')}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={[styles.milkingStatsGrid, language === 'ur' && { flexDirection: 'row-reverse' }]}>
          <View style={styles.milkingStatCard}>
            <TrendingUp size={24} color={themedColors.primary} />
            <Text style={[styles.milkingStatValue, language === 'ur' && styles.urduText]}>
              {stats.totalQuantity.toFixed(1)}L
            </Text>
            <Text style={[styles.milkingStatLabel, language === 'ur' && styles.urduText]}>
              {t('milking.totalProduction')}
            </Text>
          </View>

          <View style={styles.milkingStatCard}>
            <Calendar size={24} color={themedColors.primary} />
            <Text style={[styles.milkingStatValue, language === 'ur' && styles.urduText]}>
              {stats.averageDaily.toFixed(1)}L
            </Text>
            <Text style={[styles.milkingStatLabel, language === 'ur' && styles.urduText]}>
              {t('milking.dailyAverage')}
            </Text>
          </View>
        </View>

        {stats.monthlyTrend.length > 0 && (
          <View style={styles.milkingChartContainer}>
            <Text style={[styles.milkingChartTitle, language === 'ur' && styles.urduText]}>
              {t('milking.monthlyTrend')}
            </Text>
            <LineChart
              data={{
                labels: stats.monthlyTrend.map(item => item.month),
                datasets: [{
                  data: stats.monthlyTrend.map(item => item.quantity),
                }],
              }}
              width={screenWidth - 32}
              height={200}
              chartConfig={{
                backgroundColor: themedColors.card,
                backgroundGradientFrom: themedColors.card,
                backgroundGradientTo: themedColors.card,
                decimalPlaces: 1,
                color: (opacity = 1) => themedColors.primary || `rgba(71, 117, 234, ${opacity})`,
                labelColor: (opacity = 1) => themedColors.text || `rgba(0, 0, 0, ${opacity})`,
                style: {
                  borderRadius: 16
                },
                propsForLabels: {
                  fontSize: 10,
                }
              }}
              style={{
                marginVertical: 8,
                borderRadius: 16,
              }}
            />
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.tabContent}>
        <LoadingIndicator message={t('common.loading')} />
      </View>
    );
  }

  if (error) {
    // Check if it's a Firebase indexing error
    const isIndexError = error.includes('index') || error.includes('composite') || error.includes('query requires');

    if (isIndexError) {
      // Show empty state for indexing errors instead of technical error
      return (
        <View style={styles.tabContent}>
          <EmptyState
            title={t('milking.noRecords')}
            message={t('milking.addYourFirstRecord')}
            actionLabel={t('milking.addRecord')}
            onAction={onAddMilking}
            icon={<Milk size={48} color={themedColors.primary} />}
          />
        </View>
      );
    }

    // Show retry button for other errors
    return (
      <View style={styles.tabContent}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.milkingRetryButton}
          onPress={() => {
            clearError();
            fetchRecordsByFarm(farm.id);
            fetchStats(farm.id, selectedPeriod);
          }}
        >
          <Text style={styles.milkingRetryButtonText}>{t('common.retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (records.length === 0) {
    return (
      <View style={styles.tabContent}>
        <EmptyState
          title={t('milking.noRecords')}
          message={t('milking.addYourFirstRecord')}
          actionLabel={t('milking.addRecord')}
          onAction={onAddMilking}
          icon={<Milk size={48} color={themedColors.primary} />}
        />
      </View>
    );
  }

  return (
    <View style={styles.tabContent}>
      {renderStatsCard()}
    </View>
  );
}
