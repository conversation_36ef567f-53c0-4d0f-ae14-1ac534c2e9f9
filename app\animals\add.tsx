import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image as RNImage,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams, Redirect, Stack } from 'expo-router';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { useAnimalStore } from '@/store/animal-store';
import { useLookupStore } from '@/store/lookup-store';
import { useSettingsStore } from '@/store/settings-store';

import Button from '@/components/Button';
import Input from '@/components/Input';
import {
  Camera,
  X,
  Ruler,
  Weight,
  Calendar,
  Tag,
  Save,
  Grid,
  AlertCircle,
  CheckCircle2,
  Sparkles,
  MapPin
} from 'lucide-react-native';
import DatePickerInput from '@/components/DatePickerInput';
import { useTranslation } from '@/hooks/useTranslation';
import { generateTagId } from '@/constants/breeds';
import { analyzeAnimalImage } from '@/services/openai-service';
import { useToast } from '@/contexts/ToastContext';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { useThemeColors } from '@/hooks/useThemeColors';
import ImageCaptureButtons from '@/components/ImageCaptureButtons'; // Import the theme hook

// Add this to your imports
import { useFarmStore } from '@/store/farm-store';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';

export default function AddAnimalScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ farmId: string }>();
  const farmId = params.farmId;

  const { user } = useAuthStore(); // Assuming Animal type in store is updated with fatherId, motherId
  const { addAnimal, isLoading, animals: allAnimals, fetchAnimals: fetchAllAnimals } = useAnimalStore();
  const { openaiApiKey, aiVisionEnabled } = useSettingsStore();
  const { getLookupsByCategory } = useLookupStore();
  const themedColors = useThemeColors(); // Use the theme hook

  const { t, language } = useTranslation(); // Get translation function and current language
  const { showToast } = useToast();
  const { playFeedback } = useAudioFeedback();

  const [name, setName] = useState('');
  const [species, setSpecies] = useState('Cow'); // Default to Cow
  const [breed, setBreed] = useState('');
  const [age, setAge] = useState('');
  const [weight, setWeight] = useState('');
  const [gender, setGender] = useState<'male' | 'female'>('female'); // Default to female
  const [tagId, setTagId] = useState('');
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [birthDate, setBirthDate] = useState(new Date());
  const [fatherId, setFatherId] = useState<string | undefined>(undefined);
  const [motherId, setMotherId] = useState<string | undefined>(undefined);

  // Add these new states
  const { farms } = useFarmStore();
  const [selectedFarm, setSelectedFarm] = useState('');
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    species?: string;
    breed?: string;
    age?: string;
    weight?: string;
    imageUri?: string;
    tagId?: string;
    farm?: string;
    // fatherId?: string; // Optional, as "Bought from External" is a valid choice
    // motherId?: string; // Optional
  }>({});

  // AI Vision analysis states
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<string | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  // Flag to prevent circular dependency between age input and birth date calculation
  const [isUserTypingAge, setIsUserTypingAge] = useState(false);

  // Generate styles dynamically
  const styles = getStyles(themedColors);

  // First, check if the user is a worker, and redirect if needed
  const isOwnerOrAdmin = user?.role === 'owner' || user?.role === 'admin';

  // If the user is a caretaker, redirect them to the home screen
  if (user && !isOwnerOrAdmin) {
    console.log('Caretaker tried to access Farms screen, redirecting...');
    return <Redirect href="/" />;
  }

  // Generate tag ID when component mounts
  useEffect(() => {
    setTagId(generateTagId());
    // Fetch all animals for parent selection dropdowns
    if (user?.id) {
      // Ensure this fetchAnimals fetches all animals accessible to the user,
      // not just for a specific farm, or adjust logic as needed.
      // This assumes `useAnimalStore().animals` will be populated with all potential parents.
      fetchAllAnimals(user.id);
    }
  }, []);

  // Calculate age when birth date changes (but not when user is typing age)
  useEffect(() => {
    if (birthDate && !isUserTypingAge) {
      calculateAgeFromBirthDate();
    }
  }, [birthDate, isUserTypingAge]);


  // Calculate age from birth date with decimal precision
  const calculateAgeFromBirthDate = () => {
    const today = new Date();
    const birthYear = birthDate.getFullYear();
    const birthMonth = birthDate.getMonth();
    const birthDay = birthDate.getDate();

    // Calculate years
    let ageYears = today.getFullYear() - birthYear;

    // Calculate months difference
    let monthsDiff = today.getMonth() - birthMonth;

    // Adjust if the day hasn't occurred yet this month
    if (today.getDate() < birthDay) {
      monthsDiff--;
    }

    // Adjust years and months if months is negative
    if (monthsDiff < 0) {
      ageYears--;
      monthsDiff += 12;
    }

    // Convert to decimal years (months/12)
    const decimalAge = ageYears + (monthsDiff / 12);

    // Don't allow negative ages
    const finalAge = Math.max(0, decimalAge);

    // Round to 1 decimal place
    setAge(finalAge.toFixed(1));
  };

  // Calculate birth date based on age with decimal precision
  const calculateBirthDate = (ageInYears: string) => {
    // Only calculate if we have a valid number and it's not just a decimal point
    if (!ageInYears || ageInYears.endsWith('.') || isNaN(Number(ageInYears))) {
      return;
    }

    const totalYears = Number(ageInYears);
    // Only calculate for reasonable age values
    if (totalYears < 0 || totalYears > 50) {
      return;
    }

    const today = new Date();

    // Split into whole years and fractional part
    const wholeYears = Math.floor(totalYears);
    const fractionalYear = totalYears - wholeYears;

    // Convert fractional year to months (0.1 year = 1.2 months)
    const additionalMonths = Math.round(fractionalYear * 12);

    // Calculate birth date
    const calculatedBirthDate = new Date(today);

    // Subtract whole years
    calculatedBirthDate.setFullYear(today.getFullYear() - wholeYears);

    // Subtract additional months
    calculatedBirthDate.setMonth(calculatedBirthDate.getMonth() - additionalMonths);

    // Set flag to prevent circular dependency
    setIsUserTypingAge(true);
    setBirthDate(calculatedBirthDate);
    // Reset flag after birth date is set
    setTimeout(() => setIsUserTypingAge(false), 100);
  };



  const animalArray = React.useMemo(() => {
    const speciesLookups = getLookupsByCategory('animalSpecies');
    const breedLookups = getLookupsByCategory('animalBreed');
    if (!speciesLookups || speciesLookups.length === 0 || !breedLookups || breedLookups.length === 0) {
      // Fallback to an object with empty arrays if lookups are not ready.
      return { species: [], breeds: [] };
    }
    const speciesArray = speciesLookups.map(lookup => ({
      name: lookup.title,
      id: lookup.id,
      imageUrl: lookup?.imageUrl || 'https://images.unsplash.com/photo-1554342413-f8ebdef96b36?q=80&w=2070&auto=format&fit=crop'
    }));
    const breedsArray = breedLookups.map(lookup => ({
      name: lookup.title,
      id: lookup.id,
      parentId: lookup.parentId,
    }));
    return {
      species: speciesArray,
      breeds: breedsArray
    };
  }, [getLookupsByCategory]);

    // Find the image URL for the currently selected species
    const selectedSpeciesData = animalArray.species.find(s => s.name === species);
    const speciesImageUrl = selectedSpeciesData?.imageUrl;
  
  // Gender images based on selected species
  const getGenderImages = () => {
    switch (species) {
      case 'Cow':
        return {
          // Male: Bull image - Updated to use the new URL
          male: 'https://img.pikbest.com/png-images/20241011/a-buffalo-skull-head-is-isolated-on-a-transparent-background_10946913.png!w700wp',
          // Female: Cow image
          female: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
        };
      case 'Goat':
        return {
          // Male: Buck image
          male: 'https://img.freepik.com/free-psd/close-up-goats-isolated_23-2151840080.jpg?ga=GA1.1.1740816856.1740755798&semt=ais_hybrid&w=740',
          // Female: Doe image
          female: 'https://images.unsplash.com/photo-1533318087102-b3ad366ed041?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
        };
      case 'Poultry':
        return {
          // Male: Rooster image
          male: 'https://images.unsplash.com/photo-1612170153139-6f881ff067e0?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          // Female: Hen image
          female: 'https://images.unsplash.com/photo-1548550023-2bdb3c5beed7?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
        };
      case 'Fish':
        return {
          // For fish, we'll use the same image for both genders
          male: 'https://images.unsplash.com/photo-1524704654690-b56c05c78a00?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          female: 'https://images.unsplash.com/photo-1524704654690-b56c05c78a00?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
        };
      default:
        return {
          male: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          female: 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
        };
    }
  };

  // Get gender labels based on species

  // Handle image selection from camera or gallery
  const handleImagePick = async (useCamera: boolean) => {
    try {
      let result;

      if (useCamera) {
        // Request camera permissions
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission required', 'Camera permission is required to take photos');
          return;
        }

        // Launch camera
        result = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      } else {
        // Request media library permissions
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission required', 'Media library permission is required to select photos');
          return;
        }

        // Launch image picker
        result = await ImagePicker.launchImageLibraryAsync({
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImageUri = result.assets[0].uri;
        setImageUri(selectedImageUri);

        // Clear the image validation error if it exists
        if (validationErrors.imageUri) {
          setValidationErrors(prev => ({
            ...prev,
            imageUri: undefined
          }));
        }

        // If AI Vision is enabled and we have an API key, analyze the image
        if (aiVisionEnabled && openaiApiKey) {
          analyzeImage(selectedImageUri);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  // Analyze the selected image using OpenAI Vision API
  const analyzeImage = async (uri: string) => {
    if (!openaiApiKey) {
      setAnalysisError(t('animals.apiKeyMissing'));
      return;
    }

    setIsAnalyzing(true);
    setAnalysisResult(null);
    setAnalysisError(null);

    try {
      console.log('Analyzing image with OpenAI Vision API...');

      const result = await analyzeAnimalImage(uri, openaiApiKey);

      // Update form fields with analysis results
      setSpecies(result.species);
      setBreed(result.breed);
      setGender(result.gender === 'male' || result.gender === 'female' ? result.gender : 'female');

      if (result.estimatedAge) {
        const ageValue = result.estimatedAge.toString();
        setAge(ageValue);
        calculateBirthDate(ageValue);
      }

      // Set analysis result message
      if (language === 'ur') {
        // Urdu format with RTL considerations
        const speciesText = t(`animals.${result.species.toLowerCase()}`) || result.species;
        const breedText = result.breed !== 'Unknown' ?
          (t(`animals.${result.breed}`) || result.breed) :
          t('animals.analysisUnknown');
        const genderText = t(`animals.${result.gender}`);
        const ageText = result.estimatedAge ?
          `${t('animals.analysisApproximately')} ${result.estimatedAge} ${t('animals.analysisYearsOld')}` :
          t('animals.analysisAgeUnknown');

        setAnalysisResult(
          `${t('animals.analysisDetected')} ${speciesText} (${breedText})، ${genderText}، ${ageText} ${result.confidence}% ${t('animals.analysisWithConfidence')}`
        );
      } else {
        // English format
        setAnalysisResult(
          `Detected ${result.species} (${result.breed}), ${result.gender}, ${result.estimatedAge ? `approximately ${result.estimatedAge} years old` : 'age unknown'} with ${result.confidence}% confidence.`
        );
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      setAnalysisError(t('animals.analyzeError'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Remove the selected image
  const handleRemoveImage = () => {
    setImageUri(null);
    setAnalysisResult(null);
    setAnalysisError(null);
  };

  // Validate form fields
  const validateForm = () => {
    const errors: {
      name?: string;
      species?: string;
      breed?: string;
      age?: string;
      weight?: string;
      imageUri?: string;
      tagId?: string;
      farm?: string;
    } = {};
    // Add this validation
    if (!selectedFarm) {
      errors.farm = t('expenses.errors.farmRequired');
    }
    // Validate image
    if (!imageUri) {
      errors.imageUri = t('animals.photoRequired');
    }

    // Validate name
    if (!name.trim()) {
      errors.name = t('animals.nameRequired');
    }

    // Validate species
    if (!species) {
      errors.species = t('animals.speciesRequired');
    }

    // Validate breed
    if (!breed.trim()) {
      errors.breed = t('animals.breedRequired');
    }

    // Validate age
    if (!age.trim()) {
      errors.age = t('animals.ageRequired');
    } else if (isNaN(Number(age))) {
      errors.age = t('animals.ageNumber');
    } else if (Number(age) <= 0) {
      errors.age = t('animals.agePositive');
    }

    // Validate weight
    if (!weight.trim()) {
      errors.weight = t('animals.weightRequired');
    } else if (isNaN(Number(weight))) {
      errors.weight = t('animals.weightNumber');
    }

    // Validate tag ID
    if (!tagId.trim()) {
      errors.tagId = t('animals.tagIdRequired');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      // Scroll to top to show validation errors
      Alert.alert(t('common.validationError'), t('common.fillRequiredFields'));
      return;
    }

    if (!user) {
      Alert.alert(t('common.error'), t('animals.loginRequired'));
      return;
    }

    try {
      // Ensure all required fields are present and properly formatted
      const animalData = {
        name: name.trim(),
        species: species.trim(),
        breed: breed.trim(),
        age: isNaN(Number(age)) ? 0 : Number(age),
        weight: isNaN(Number(weight)) ? 0 : Number(weight),
        gender,
        tagId: tagId.trim(),
        imageUri: imageUri || undefined,
        birthDate: birthDate.getTime(),
        ownerId: user.id,
        tenantId: user.id, // Add tenantId which is required by the Animal type
        // farmId: farmId || undefined, // Add farmId if provided

        // Add default values for health tracking
        healthCheckInterval: 7, // Default to 7 days
        nextHealthCheck: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
        hasHealthIssues: false,
        farmId: selectedFarm, // Add this line
        fatherId: fatherId, // Will be animal_id or 'BOUGHT_EXTERNAL' or undefined
        motherId: motherId, // Will be animal_id or 'BOUGHT_EXTERNAL' or undefined
      };

      await addAnimal(animalData);

      // Play success feedback
      playFeedback('success');

      // Show success toast
      showToast(t('animals.addSuccess'), 'success');

      // Navigate back to the farm detail screen if farmId is provided, otherwise to animals screen
      if (farmId) {
        router.push(`/farms/${farmId}`);
      } else {
        router.push('/(tabs)/animals');
      }
    } catch (error) {
      console.error('Error adding animal:', error);
      Alert.alert(t('common.error'), t('animals.addError'));
    }
  };

  // Get breed options as string array for dropdown
  // Get breed options as DropdownItem array for GenericDropdown
  const getBreedDropdownItems = React.useCallback((): DropdownItem[] => {
    // Find the selected species object from the lookups to get its ID
    const selectedSpeciesObject = animalArray.species.find(s => s.name === species);

    // If no species is selected or found, or breeds are not loaded, return empty.
    if (!selectedSpeciesObject || !animalArray.breeds) {
      return [];
    }

    // Filter breeds from the lookup store based on the parentId (which is the species ID)
    const options = animalArray.breeds.filter(b => b.parentId === selectedSpeciesObject.id);

    const dropdownItems = options.map(option => ({
      id: option.name, // The value to be stored in the `breed` state is the name
      label: language === 'ur' ? (t(`animals.${option.name}`) || option.name) : option.name,
    }));

    // If a breed is already set (e.g., from AI analysis) and it's not in the list, add it so it can be displayed.
    if (breed && breed.trim() && !dropdownItems.find(item => item.id === breed)) {
      dropdownItems.push({
        id: breed,
        label: breed,
      });
    }

    return dropdownItems;
  }, [species, breed, animalArray, language, t]);

  // Prepare items for parent dropdowns
  const getParentDropdownItems = React.useCallback((parentGender: 'male' | 'female'): DropdownItem[] => {
    const externalOption: DropdownItem = {
      id: 'BOUGHT_EXTERNAL', // Special ID for this option
      label: t('animals.boughtFromExternal'),
    };

    if (!species || !allAnimals) return [externalOption];

    const filtered = allAnimals.filter(
      animal => animal.species === species && animal.gender === parentGender  && animal.farmId === selectedFarm
    );

    return [
      externalOption,
      ...filtered.map(animal => ({
        id: animal.id,
        label: animal.name,
      })),
    ];
  }, [allAnimals, species, t, language]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('animals.addAnimal'), // Always English
          headerBackTitle: t('common.back'), // Keep back button translatable or set to English
          headerTitleStyle: { fontWeight: 'bold', color: themedColors.text },
          headerStyle: {
            backgroundColor: themedColors.background,
          },
          headerTintColor: themedColors.text,
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.imageSection}>
          <View style={styles.imageUploadCard}>

            {imageUri ? (
              <View style={styles.imageContainer}>
                <RNImage source={{ uri: imageUri }} style={styles.image} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={handleRemoveImage}
                >
                  <X size={20} color="white" />
                </TouchableOpacity>

                {/* Success indicator */}
                <View style={styles.successIndicator}>
                  <CheckCircle2 size={24} color={themedColors.success} strokeWidth={2.5} />
                  <Text style={[styles.successText, language === 'ur' ? styles.urduText : null]}>{t('animals.photoAdded')}</Text>
                </View>
              </View>
            ) : (
              <View style={[
                styles.imagePlaceholder,
                validationErrors.imageUri ? styles.inputError : null
              ]}>
                {/* Animal image based on selected species */}
                {speciesImageUrl ? (
                  <RNImage source={{ uri: speciesImageUrl }} style={styles.animalSilhouette} />
                ) : (
                  /* Default icon if no specific image is available */
                  <Camera size={60} color={validationErrors.imageUri ? themedColors.error : themedColors.textSecondary} />
                )}

                <Text style={[
                  styles.imagePlaceholderText,
                  validationErrors.imageUri ? { color: themedColors.error } : null // Use themed error color
                ]}>
                  {t('animals.addPhoto')}*
                </Text>
              </View>
            )}

            {validationErrors.imageUri && (
              <View style={styles.errorContainer}>
                <AlertCircle size={16} color={themedColors.error} />
                <Text style={styles.fieldErrorText}>{validationErrors.imageUri}</Text>
              </View>
            )}



            <ImageCaptureButtons
              onTakePhoto={() => handleImagePick(true)}
              onChooseFromLibrary={() => handleImagePick(false)}
            />
          </View>

          {isAnalyzing && (
            <View style={styles.analysisStatus}>
              <ActivityIndicator size="small" color={themedColors.primary} />
            </View>
          )}

          {analysisResult && (
            <View style={styles.analysisResult}>
              <CheckCircle2 size={16} color={themedColors.success} />
              <Text style={[styles.analysisResultText, language === 'ur' ? styles.urduText : null]}>{analysisResult}</Text>
            </View>
          )}

          {analysisError && (
            <View style={styles.analysisError}>
              <AlertCircle size={16} color={themedColors.error} />
              <Text style={[styles.analysisErrorText, language === 'ur' ? styles.urduText : null]}>{analysisError}</Text>
            </View>
          )}

          {aiVisionEnabled && openaiApiKey && imageUri && !isAnalyzing && !analysisResult && !analysisError && (
            <TouchableOpacity
              style={styles.analyzeButton}
              onPress={() => analyzeImage(imageUri)}
            >
              <Sparkles size={16} color={themedColors.primary} />
              <Text style={[styles.analyzeButtonText, language === 'ur' ? styles.urduText : null]}>{t('animals.analyzeWithAI')}</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.formSection}>
          <Input
            label={`${t('animals.name')}*`}
            placeholder={t('animals.namePlaceholder')}
            value={name}
            onChangeText={setName}
            error={validationErrors.name ? t('animals.nameRequired') : undefined}
            style={[styles.compactInput]}
            textStyle={language === 'ur' ? styles.urduText : undefined}
          />

          <Text style={[styles.sectionLabel, language === 'ur' ? styles.urduText : null, { marginTop: 10 }]}>{t('animals.animalSpecies')}*</Text>
          <View style={styles.speciesContainer}>
            {animalArray.species.map((item:any) => (
              <TouchableOpacity
                key={item.name}
                style={[
                  styles.speciesItem,
                  species === item.name && styles.speciesItemSelected,
                  validationErrors.species ? styles.inputError : null
                ]}
                onPress={() => {
                  setSpecies(item.name);
                  setBreed(''); // Reset breed when species changes
                  // Also clear breed validation error if it exists
                  if (validationErrors.breed) {
                    setValidationErrors(prev => ({ ...prev, breed: undefined }));
                  }
                }}
              >
                <RNImage
                  source={{ uri: item.imageUrl }}
                  style={styles.speciesImage}
                />
                <Text style={[
                  styles.speciesText,
                  species === item.name && styles.speciesTextSelected
                ]}>
                  {t(`animals.${item.name.toLowerCase()}`) || item.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          {validationErrors.species && (
            <Text style={styles.fieldErrorText}>{validationErrors.species}</Text>
          )}

          <Text style={[styles.label, language === 'ur' ? styles.urduText : null, { marginTop: 16 }]}>
            {`${t('animals.breed')}*`}
          </Text>
          <View style={styles.dropdownContainer}>
            <GenericDropdown
              placeholder={t('animals.breedSelectPlaceholder')}
              items={getBreedDropdownItems()}
              value={breed}
              onSelect={setBreed}
              error={validationErrors.breed}
              allowCustomOption={true}
              customOptionLabel={t('animals.addNewBreed')}
              onAddCustomOption={(newBreed) => {
                setBreed(newBreed);
                // Clear any existing breed validation error
                if (validationErrors.breed) {
                  setValidationErrors(prev => ({
                    ...prev,
                    breed: undefined
                  }));
                }
              }}
              modalTitle={t('animals.breedSelectPlaceholder')}
              searchPlaceholder={t('animals.searchBreeds')}
            />
          </View>
          {/* If GenericDropdown doesn't display its own error text, you might add it here.
              However, consistency with Input and other GenericDropdowns suggests it handles the error prop. */}
          {validationErrors.breed && (
            <Text style={styles.fieldErrorText}>{validationErrors.breed}</Text>
          )}

          <Text style={[styles.sectionLabel, language === 'ur' ? styles.urduText : null, {marginTop:15}]}>{t('animals.gender')}</Text>
          <View style={styles.genderContainer}>
            <TouchableOpacity
              style={[
                styles.genderItem,
                gender === 'male' && styles.genderItemSelected
              ]}
              onPress={() => setGender('male')}
            >
              <RNImage
                source={{ uri: getGenderImages().male }}
                style={styles.genderImage}
              />
              <Text style={[
                styles.genderText,
                gender === 'male' && styles.genderTextSelected
              ]}>
                {t('animals.male')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.genderItem,
                gender === 'female' && styles.genderItemSelected
              ]}
              onPress={() => setGender('female')}
            >
              <RNImage
                source={{ uri: getGenderImages().female }}
                style={styles.genderImage}
              />
              <Text style={[
                styles.genderText,
                gender === 'female' && styles.genderTextSelected
              ]}>
                {t('animals.female')}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={[styles.row, styles.compactRow]}>
            <View style={styles.halfInput}>
              <Input
                label={`${t('animals.age')}*`}
                placeholder={t('animals.agePlaceholder')}
                value={age}
                onChangeText={(value) => {
                  // Simple direct update without complex logic for testing
                  console.log("Shery", value);
                  setAge(value);
                  setIsUserTypingAge(true);
                }}
                onBlur={() => {
                  // Calculate birth date when user focuses out from age field
                  if (age && !age.endsWith('.') && !isNaN(Number(age))) {
                    calculateBirthDate(age);
                  }
                  setIsUserTypingAge(false);
                }}
                keyboardType="numeric"
                error={validationErrors.age}
                leftIcon={<Ruler size={18} color={themedColors.textSecondary} />}
                // Age field is now editable and accepts decimal values
                style={styles.compactInput}
              />
            </View>

            <View style={styles.halfInput}>
              <Input
                label={`${t('animals.animalWeight')}*`}
                placeholder={t('animals.weightInputPlaceholder')}
                value={weight}
                onChangeText={setWeight}
                keyboardType="decimal-pad"
                error={validationErrors.weight}
                leftIcon={<Weight size={18} color={themedColors.textSecondary} />}
                style={styles.compactInput}
              />
            </View>
          </View>

          <View style={[styles.row, styles.compactRow]}>
            <View style={styles.halfInput}>
              <DatePickerInput
                label={`${t('animals.animalBirthdate')}*`}
                value={birthDate}
                onChange={(date) => {
                  setBirthDate(date);
                  // Reset the typing flag when user manually changes birth date
                  setIsUserTypingAge(false);
                }}
                leftIcon={<Calendar size={18} color={themedColors.textSecondary} />}
              />
            </View>

            <View style={styles.halfInput}>
              <Input
                label={`${t('animals.animalTagId')}*`}
                placeholder={t('animals.tagIdInputPlaceholder')}
                value={tagId}
                onChangeText={setTagId}
                leftIcon={<Tag size={18} color={themedColors.textSecondary} />}
                error={validationErrors.tagId}
                style={styles.compactInput}
              />
            </View>
          </View>


        </View>
        <View style={styles.formGroup}>
          <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
            {t('farms.selectFarm')}*
          </Text>
          <View style={styles.dropdownContainer}>
            <GenericDropdown
              placeholder={t('farms.selectFarmPlaceholder')}
              items={farms.map(farm => ({
                id: farm.id,
                label: farm.name,
                description: typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location',
                icon: <MapPin size={20} color={themedColors.primary} />
              }))}
              value={selectedFarm}
              onSelect={setSelectedFarm}
              modalTitle={t('farms.selectFarm')}
              searchPlaceholder={t('farms.searchFarms')}
              error={validationErrors.farm}
              renderIcon={false} // Add this prop to hide the icon
            // or remove the renderIcon prop if it exists
            />
          </View>


             {/* Parent Selection Dropdowns - Enabled when species is selected */}
             {species && selectedFarm && (
            <>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null, { marginTop: 16 }]}>
                {t('animals.father')}
              </Text>
              <View style={styles.dropdownContainer}>
                <GenericDropdown
                  placeholder={t('animals.selectFatherPlaceholder')}
                  items={getParentDropdownItems('male')}
                  value={fatherId}
                  onSelect={setFatherId}
                  modalTitle={t('animals.selectFather')}
                  searchPlaceholder={t('animals.searchAnimals')}
                  // error={validationErrors.fatherId} // Add if strict validation needed
                />
              </View>
              {/* {validationErrors.fatherId && <Text style={styles.fieldErrorText}>{validationErrors.fatherId}</Text>} */}


              <Text style={[styles.label, language === 'ur' ? styles.urduText : null, { marginTop: 16 }]}>
                {t('animals.mother')}
              </Text>
              <View style={styles.dropdownContainer}>
                <GenericDropdown
                  placeholder={t('animals.selectMotherPlaceholder')}
                  items={getParentDropdownItems('female')}
                  value={motherId}
                  onSelect={setMotherId}
                  modalTitle={t('animals.selectMother')}
                  searchPlaceholder={t('animals.searchAnimals')}
                  // error={validationErrors.motherId} // Add if strict validation needed
                />
              </View>
              {/* {validationErrors.motherId && <Text style={styles.fieldErrorText}>{validationErrors.motherId}</Text>} */}
            </>
          )}
        </View>
        

        <View style={styles.submitButtonContainer}>
          <Button
            title={t('animals.saveAnimal')}
            onPress={handleSubmit}
            leftIcon={<Save size={20} color="white" />}
            isLoading={isLoading}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  imageSection: {
    padding: 8,
    paddingTop: 4,
    alignItems: 'center',
    backgroundColor: themedColors.card, // Added themed background color

  },
  imageUploadCard: {
    width: '100%',
    backgroundColor: themedColors.card, // Use themed card color
    borderRadius: 16,
    padding: 12,
    paddingTop: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: themedColors.border, // Use themed border color
    marginBottom: 12,
    // elevation: 2,
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 4,
    // Theme-aware shadow properties
    ...(themedColors.isDarkMode ? {
      shadowColor: 'rgba(255,255,255,0.1)',
      shadowOpacity: 1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 4,
      elevation: 2,
    } : {
      shadowColor: '#000',
      shadowOpacity: 0.1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 4,
      elevation: 3, // Slightly more pronounced shadow in light mode
    }),
  },
  imageUploadTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: themedColors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  imageContainer: {
    width: '100%',
    height: 250,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  successIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: themedColors.isDarkMode ? 'rgba(31, 41, 55, 0.9)' : 'rgba(255, 255, 255, 0.9)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    gap: 8,
  },
  successText: {
    color: themedColors.success,
    fontWeight: 'bold',
    fontSize: 16,
  },
  imagePlaceholder: {
    width: 220,
    height: 220,
    borderRadius: 16,
    backgroundColor: themedColors.card, // Use themed card color
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 2,
    borderColor: themedColors.border, // Use themed border color
    borderStyle: 'dashed',
    overflow: 'hidden',
  },
  animalSilhouette: {
    width: 120,
    height: 120,
    opacity: 0.5,
    marginBottom: 8,
    borderRadius: 60,
  },
  imagePlaceholderText: {
    marginTop: 8,
    color: themedColors.textSecondary, // Use themed text secondary color
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  imageInstructions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: themedColors.primaryLight,
    padding: 12,
    borderRadius: 8,
    width: '100%',
    gap: 12,
  },
  instructionText: {
    color: themedColors.primary,
    fontSize: 16,
    flex: 1,
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 10,
    marginTop: 10
  },

  analysisStatus: {
    alignItems: 'center',
    backgroundColor: themedColors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  analysisResult: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.successLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  analysisResultText: {
    marginLeft: 8,
    color: themedColors.success,
    fontSize: 14,
  },
  analysisError: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.errorLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  analysisErrorText: {
    marginLeft: 8,
    color: themedColors.error,
    fontSize: 14,
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: themedColors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  analyzeButtonText: {
    marginLeft: 8,
    color: themedColors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  formSection: {
    padding: 12,
    paddingTop: 4,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 8,
  },
  speciesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 6, // Reduced to accommodate error text
  },
  speciesItem: {
    width: '48%',
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 8,
    marginBottom: 6,
    alignItems: 'center',
    backgroundColor: themedColors.card,
  },
  speciesItemSelected: {
    borderColor: themedColors.primary,
    backgroundColor: themedColors.primaryLight,
  },
  speciesImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: 6,
  },
  speciesText: {
    fontSize: 14,
    color: themedColors.text,
  },
  speciesTextSelected: {
    color: themedColors.primary,
    fontWeight: '500',
  },
  genderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  genderItem: {
    width: '48%',
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    backgroundColor: themedColors.card,
  },
  genderItemSelected: {
    borderColor: themedColors.primary,
    backgroundColor: themedColors.primaryLight,
  },
  genderImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: 6,
  },
  genderText: {
    fontSize: 14,
    color: themedColors.text,
  },
  genderTextSelected: {
    color: themedColors.primary,
    fontWeight: '500',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  compactRow: {
    // marginBottom: 4,
    marginTop: 10
  },
  halfInput: {
    width: '48%',
  },
  compactInput: {
    marginBottom: 0,
  },
  buttonContainer: {
    padding: 12,
    marginBottom: 16,
  },

  errorText: {
    color: themedColors.error,
  },
  fieldErrorText: {
    color: themedColors.error,
    fontSize: 12,
    marginBottom: 4,
    alignSelf: 'flex-start',
    marginLeft: 4,
  },
  inputError: {
    borderColor: themedColors.error,
  },
  submitButtonContainer: {
    padding: 16,
    paddingTop: 8,
  },
  formGroup: {
    marginBottom: 20,
    marginHorizontal: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 8,
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    backgroundColor: themedColors.background,
    height: 52, // Match input field height
    justifyContent: 'center', // Center content vertically
  },
  dropdownWrapper: {
    flex: 1,
    paddingHorizontal: 8, // Add padding inside dropdown
  },
});
