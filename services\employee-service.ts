import { firestore } from '@/config/firebase';
import { collection, getDocs, query, where, doc, updateDoc, deleteDoc, getDoc, setDoc } from 'firebase/firestore';

export interface Employee {
  id?: string;
  name: string;
  email: string;
  phone_number: string;
  cnic: string;
  age: number;
  gender: string;
  role: string;
  joining_date: Date;
  status: string;
  ownerId: string;
  farmId?: string;
  farmIds?: string[]; // Array of farm IDs this employee has access to
  assignedFarms?: string[]; // Array of farm IDs this employee is assigned to
  assignedFarmIds?: string[]; // Consolidated farm IDs field
  password?: string;
  photo?: string; // URL to employee photo
  // Multi-tenancy fields
  tenantId?: string; // ID of the owner (tenant) who owns the farm this employee is associated with
  uid?: string; // Firebase Auth UID
  // Timestamps
  createdAt?: Date;
  updatedAt?: Date;
  isEmployee?: boolean; // Flag to identify as employee
  // Additional fields for farm management
  farmCount?: number; // Number of farms this employee is assigned to
  farmNames?: string; // Comma-separated farm names
  farmDetails?: any[]; // Detailed farm information
}

export const addEmployee = async (employeeData: Partial<Employee>): Promise<string> => {
  try {
    // Create the employee document
    const employeeRef = doc(collection(firestore, 'users'));
    const employeeId = employeeRef.id;
    
    // Set default values and add ID
    const newEmployee = {
      ...employeeData,
      id: employeeId,
      role: employeeData.role || 'caretaker',
      status: employeeData.status || 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Ensure assignedFarms matches farmIds
    if (employeeData.farmIds) {
      newEmployee.assignedFarms = employeeData.farmIds;
    }
    
    await setDoc(employeeRef, newEmployee);

    
    // Note: Staff is now managed through Users collection only
    // assignedFarmIds are stored in the user document
    
    return employeeId;
  } catch (error) {
    throw error;
  }
};

export const getEmployeesByOwner = async (ownerId: string): Promise<Employee[]> => {
  try {
    // Use the new getAllFarmsUsers function to get all users (including owner, admins, caretakers)
    return await getAllFarmsUsers(ownerId);
  } catch (error) {
    console.error('Error getting employees by owner:', error);
    throw error;
  }
};

/**
 * Get only staff members (admins and caretakers) excluding the owner
 */
export const getStaffByOwner = async (ownerId: string): Promise<Employee[]> => {
  try {
    const allUsers = await getAllFarmsUsers(ownerId);
    // Filter out the owner, keep only admins and caretakers
    return allUsers.filter(user => (user as any).role !== 'owner');
  } catch (error) {
    console.error('Error getting staff by owner:', error);
    throw error;
  }
};

/**
 * Get all users (owners, admins, caretakers) who belong to the owner's farms
 * Includes the owner at the top, then admins, then caretakers
 */
export const getAllFarmsUsers = async (ownerId: string): Promise<Employee[]> => {
  try {
    // First, get all farms owned by this owner
    const farmsRef = collection(firestore, 'farms');
    const farmsQuery = query(farmsRef, where('ownerId', '==', ownerId));
    const farmsSnapshot = await getDocs(farmsQuery);

    // Extract farm IDs owned by this owner
    const ownerFarmIds = farmsSnapshot.docs.map(doc => doc.id);

    if (ownerFarmIds.length === 0) {
      return [];
    }

    // Get ALL users (not just employees) to include owners and admins
    const usersRef = collection(firestore, 'users');
    const allUsersSnapshot = await getDocs(usersRef);

    const uniqueUsers = new Map<string, Employee>();

    allUsersSnapshot.forEach((doc) => {
      const userData = doc.data();
      const assignedFarmIds = userData.assignedFarmIds || userData.assignedFarms || userData.farmIds || [];

      // Check if user belongs to owner's farms by:
      // 1. Being the owner themselves
      // 2. Having ownerId matching (employee added by this owner)
      // 3. Being assigned to at least one farm owned by this owner
      const isOwnerThemselves = doc.id === ownerId;
      const belongsToOwner = isOwnerThemselves ||
        userData.ownerId === ownerId ||
        (Array.isArray(assignedFarmIds) && assignedFarmIds.some((farmId: string) => ownerFarmIds.includes(farmId)));

      if (belongsToOwner && !uniqueUsers.has(doc.id)) {
        // Get all farms this user is assigned to (from owner's farms)
        let userFarms = [];
        if (isOwnerThemselves) {
          // Owner has access to all their farms
          userFarms = ownerFarmIds;
        } else {
          // Other users only have access to assigned farms
          userFarms = Array.isArray(assignedFarmIds)
            ? assignedFarmIds.filter((farmId: string) => ownerFarmIds.includes(farmId))
            : [];
        }

        uniqueUsers.set(doc.id, {
          ...userData,
          id: doc.id,
          assignedFarmIds: userFarms,
          farmCount: userFarms.length,
          role: userData.role || 'caretaker', // Ensure role is set
        } as unknown as Employee);
      }
    });

    // Convert to array and sort: Owner first, then Admin, then Caretaker
    const sortedUsers = Array.from(uniqueUsers.values()).sort((a, b) => {
      const roleOrder = { 'owner': 0, 'admin': 1, 'caretaker': 2 };
      const aRole = (a as any).role || 'caretaker';
      const bRole = (b as any).role || 'caretaker';

      const aOrder = roleOrder[aRole as keyof typeof roleOrder] ?? 3;
      const bOrder = roleOrder[bRole as keyof typeof roleOrder] ?? 3;

      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }

      // If same role, sort by name
      const aName = (a as any).name || '';
      const bName = (b as any).name || '';
      return aName.localeCompare(bName);
    });

    return sortedUsers;
  } catch (error) {
    console.error('Error getting all farms users:', error);
    throw error;
  }
};

/**
 * Helper function to verify if a farm belongs to a specific owner
 */
export const verifyFarmOwnership = async (farmId: string, ownerId: string): Promise<boolean> => {
  try {
    const farmRef = doc(firestore, 'farms', farmId);
    const farmDoc = await getDoc(farmRef);

    if (!farmDoc.exists()) {
      return false;
    }

    const farmData = farmDoc.data();
    return farmData.ownerId === ownerId;
  } catch (error) {
    console.error('Error verifying farm ownership:', error);
    return false;
  }
};

export const getEmployeesByFarm = async (farmId: string, requestingOwnerId?: string): Promise<Employee[]> => {
  try {
    // If requestingOwnerId is provided, verify farm ownership first
    if (requestingOwnerId) {
      const isOwner = await verifyFarmOwnership(farmId, requestingOwnerId);
      if (!isOwner) {
        throw new Error('Access denied: You do not own this farm');
      }
    }

    // Use the new staff service to get staff from Users collection
    const usersRef = collection(firestore, 'users');
    const q = query(usersRef, where('assignedFarmIds', 'array-contains', farmId));
    const querySnapshot = await getDocs(q);

    const employees: Employee[] = [];
    querySnapshot.forEach((doc) => {
      const userData = doc.data();
      employees.push({
        id: doc.id,
        ...userData,
      } as Employee);
    });

    return employees;
  } catch (error) {
    console.error('Error getting employees by farm:', error);
    throw error;
  }
};

export const updateEmployee = async (id: string, data: Partial<Employee>): Promise<void> => {
  try {
    const employeeRef = doc(firestore, 'users', id);
    
    // Note: Staff is now managed through Users collection only
    // assignedFarmIds are stored in the user document
    
    // Update the employee document
    await updateDoc(employeeRef, {
      ...data,
      updatedAt: new Date()
    });
    
  } catch (error) {
    throw error;
  }
};

export const updateEmployeeStatus = async (id: string, status: 'active' | 'inactive') => {
  try {
    const userRef = doc(firestore, 'users', id);
    
    // Update only the status field
    await updateDoc(userRef, {
      status,
      updatedAt: Date.now()
    });

    return {
      id,
      status
    };
  } catch (error) {
    throw error;
  }
};

export const deleteEmployee = async (id: string) => {
  try {
    // First, get the employee data to find the farmId
    const userRef = doc(firestore, 'users', id);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('Employee not found');
    }

    // Delete the user document
    await deleteDoc(userRef);

    // Note: Staff is now managed through Users collection only
    // No need to update farm staff arrays

    return id;
  } catch (error) {
    throw error;
  }
};

/**
 * Get employees with their farm details for better visibility
 */
export const getEmployeesWithFarmDetails = async (ownerId: string): Promise<(Employee & { farmDetails?: any[] })[]> => {
  try {
    // Get unique employees
    const employees = await getAllFarmsUsers(ownerId);

    // Get all farms owned by this owner
    const farmsRef = collection(firestore, 'farms');
    const farmsQuery = query(farmsRef, where('ownerId', '==', ownerId));
    const farmsSnapshot = await getDocs(farmsQuery);

    const farmsMap = new Map();
    farmsSnapshot.forEach((doc) => {
      farmsMap.set(doc.id, { id: doc.id, ...doc.data() });
    });

    // Add farm details to each employee
    const employeesWithDetails = employees.map(employee => {
      const assignedFarmIds = (employee as any).assignedFarmIds || employee.assignedFarms || employee.farmIds || [];
      const farmDetails = assignedFarmIds
        .map((farmId: string) => farmsMap.get(farmId))
        .filter(Boolean);

      return {
        ...employee,
        farmDetails,
        farmNames: farmDetails.map((farm: any) => farm.name).join(', '),
      };
    });

    return employeesWithDetails;
  } catch (error) {
    console.error('Error getting employees with farm details:', error);
    throw error;
  }
};

/**
 * Get all employees accessible to a user based on their role and farm ownership
 * For owners: returns employees from all their farms
 * For admins: returns employees from farms they have access to
 */
export const getAccessibleEmployees = async (userId: string, userRole: string): Promise<Employee[]> => {
  try {
    if (userRole === 'owner') {
      // For owners, get all employees from their farms
      return await getEmployeesByOwner(userId);
    } else if (userRole === 'admin') {
      // For admins, get employees from farms they have access to
      const userRef = doc(firestore, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const assignedFarmIds = userData.assignedFarmIds || [];

      if (assignedFarmIds.length === 0) {
        return [];
      }

      // Get employees from all assigned farms
      const allEmployees: Employee[] = [];
      for (const farmId of assignedFarmIds) {
        const farmEmployees = await getEmployeesByFarm(farmId);
        // Avoid duplicates
        farmEmployees.forEach(employee => {
          if (!allEmployees.find(emp => emp.id === employee.id)) {
            allEmployees.push(employee);
          }
        });
      }

      return allEmployees;
    } else {
      // Caretakers typically don't manage other employees
      return [];
    }
  } catch (error) {
    console.error('Error getting accessible employees:', error);
    throw error;
  }
};

// syncFarmStaffCount function removed - staff count is now calculated dynamically from Users collection

export const getEmployeeById = async (id: string, requestingOwnerId?: string): Promise<Employee> => {
  try {
    const userRef = doc(firestore, 'users', id);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('Employee not found');
    }

    const userData = userDoc.data();

    // If requestingOwnerId is provided, verify the employee belongs to the owner
    if (requestingOwnerId) {
      const assignedFarmIds = userData.assignedFarmIds || userData.assignedFarms || userData.farmIds || [];

      // Get owner's farms
      const farmsRef = collection(firestore, 'farms');
      const farmsQuery = query(farmsRef, where('ownerId', '==', requestingOwnerId));
      const farmsSnapshot = await getDocs(farmsQuery);
      const ownerFarmIds = farmsSnapshot.docs.map(doc => doc.id);

      // Check if employee belongs to owner
      const belongsToOwner = userData.ownerId === requestingOwnerId ||
        (Array.isArray(assignedFarmIds) && assignedFarmIds.some((farmId: string) => ownerFarmIds.includes(farmId)));

      if (!belongsToOwner) {
        throw new Error('Access denied: Employee does not belong to your farms');
      }
    }

    // Ensure farmIds is properly extracted and formatted
    let farmIds = userData.farmIds || userData.assignedFarms || userData.assignedFarmIds || [];

    // If farmIds is not an array, convert it to an array
    if (farmIds && !Array.isArray(farmIds)) {
      farmIds = [farmIds];
    }

    // Ensure assignedFarms is properly extracted and formatted
    let assignedFarms = userData.assignedFarms || userData.farmIds || userData.assignedFarmIds || [];

    // If assignedFarms is not an array, convert it to an array
    if (assignedFarms && !Array.isArray(assignedFarms)) {
      assignedFarms = [assignedFarms];
    }

    return {
      ...userData,
      id: userDoc.id,
      farmIds: farmIds,
      assignedFarms: assignedFarms,
      assignedFarmIds: userData.assignedFarmIds || assignedFarms,
    } as unknown as Employee;
  } catch (error) {
    console.error('Error getting employee by ID:', error);
    throw error;
  }
};

