import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { MapPin, Cat, Users } from 'lucide-react-native';
import { Farm, FarmStatus } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';


interface FarmCardProps {
  farm: Farm;
  onPress: (farmId: string) => void;
  cardStyle?: object;
  staffCount?: number; // Dynamic staff count from Users collection
}

const FarmCard: React.FC<FarmCardProps> = ({ farm, onPress, cardStyle, staffCount = 0 }) => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const styles = getStyles(themedColors, language);


  const handlePress = () => {
    try {
      console.log('FarmCard: handlePress called for farm:', farm?.id, farm?.name);

      if (!farm || !farm.id) {
        console.error('FarmCard: Invalid farm data', farm);
        return;
      }

      console.log('FarmCard: Calling onPress with farmId:', farm.id);
      onPress(farm.id);
    } catch (error) {
      console.error('FarmCard: Error in onPress handler', error);
    }
  };

  // Safety check for farm data
  if (!farm) {
    console.error('FarmCard: No farm data provided');
    return null;
  }

  // Get address from location
  const address = typeof farm.location === 'string' ? farm.location : farm.location?.address || t('common.noAddress');

  return (
    <TouchableOpacity
      style={[styles.farmCard, cardStyle, { backgroundColor: themedColors.card }]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.cardContent}>
        {/* Left section - Farm Image */}
        <View style={styles.imageSection}>
          {farm.photoURL ? (
            <Image
              source={{ uri: farm.photoURL }}
              style={styles.farmImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderText}>{farm.name.charAt(0)}</Text>
            </View>
          )}
        </View>

        {/* Right section - Farm Details */}
        <View style={styles.detailsSection}>
          <View style={[
            styles.farmCardHeader,
            language === 'ur' && styles.urduFarmCardHeader
          ]}>
            <Text style={[
              styles.farmName,
              language === 'ur' && styles.urduText
            ]}>{farm.name}</Text>
            <View style={[
              styles.statusBadge,
              farm.status === FarmStatus.ACTIVE && styles.statusActive,
              farm.status === FarmStatus.INACTIVE && styles.statusInactive,
              farm.status === FarmStatus.PENDING && styles.statusPending,
              farm.status === FarmStatus.COMPLETED && styles.statusCompleted,
              language === 'ur' && styles.urduStatusBadge
            ]}>
              <Text style={[
                styles.statusText,
                language === 'ur' && styles.urduText
              ]}>
                {t(`farms.status${farm.status.charAt(0).toUpperCase() + farm.status.slice(1)}`)}
              </Text>
            </View>
          </View>

          {/* Address */}
          <View style={[
            styles.infoRow,
            language === 'ur' && { flexDirection: 'row-reverse' }
          ]}>
            <View style={[
              styles.iconContainer,
              language === 'ur' && styles.urduIconContainer
            ]}>
              <MapPin size={14} color={themedColors.primary} strokeWidth={1.5} />
            </View>
            <Text style={[
              styles.infoText,
              language === 'ur' && styles.urduText
            ]} numberOfLines={1} ellipsizeMode="tail">
              {t('farms.address')}: {address}
            </Text>
          </View>

          {/* Animal Count */}
          <View style={[
            styles.infoRow,
            language === 'ur' && { flexDirection: 'row-reverse' }
          ]}>
            <View style={[
              styles.iconContainer,
              language === 'ur' && styles.urduIconContainer
            ]}>
              <Cat size={14} color={themedColors.primary} strokeWidth={1.5} />
            </View>
            <Text style={[
              styles.infoText,
              language === 'ur' && styles.urduText
            ]}>
              {t('farms.animalCount')}: {farm.animalCount || 0}
            </Text>
          </View>

          {/* Staff Count */}
          <View style={[
            styles.infoRow,
            language === 'ur' && { flexDirection: 'row-reverse' }
          ]}>
            <View style={[
              styles.iconContainer,
              language === 'ur' && styles.urduIconContainer
            ]}>
              <Users size={14} color={themedColors.primary} strokeWidth={1.5} />
            </View>
            <Text style={[
              styles.infoText,
              language === 'ur' && styles.urduText
            ]}>
              {t('farms.staffCount')}: {staffCount}
            </Text>
          </View>


        </View>
      </View>
    </TouchableOpacity>
  );
};

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  farmCard: {
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: themedColors.isDarkMode ? '#000' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: themedColors.isDarkMode ? 0.2 : 0.08,
    shadowRadius: themedColors.isDarkMode ? 3 : 2,
    elevation: themedColors.isDarkMode ? 1 : 2,
    borderWidth: themedColors.isDarkMode ? 1 : 0,
    borderColor: themedColors.isDarkMode ? themedColors.border : 'transparent',
    overflow: 'hidden',
  },
  cardContent: {
    flexDirection: 'row',
    height: 160,
  },
  imageSection: {
    width: '30%',
    position: 'relative',
  },
  farmImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: themedColors.primaryLight || (themedColors.isDarkMode ? '#A5B4FC' : '#E0E7FF'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.primary,
  },
  detailsSection: {
    width: '70%',
    padding: 12,
    justifyContent: 'space-between',
  },
  farmCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  urduFarmCardHeader: {
    flexDirection: 'row-reverse',
    justifyContent: 'flex-start',
  },
  farmName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
    flex: 1,
    textAlign: 'left',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  infoText: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginLeft: 6,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  urduStatusBadge: {
    marginLeft: 0, // Remove left margin in RTL
    marginRight: 8, // Add right margin in RTL
  },
  statusActive: {
    backgroundColor: '#10B981',
  },
  statusInactive: {
    backgroundColor: '#6B7280',
  },
  statusPending: {
    backgroundColor: '#F59E0B',
  },
  statusCompleted: {
    backgroundColor: '#3B82F6',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  farmLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: themedColors.primaryLight || (themedColors.isDarkMode ? 'rgba(165, 180, 252, 0.2)' : 'rgba(224, 231, 255, 0.5)'),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 0,
    flexShrink: 0,
  },
  urduIconContainer: {
    marginRight: 0,
    marginLeft: 8,
  },
  urduText: {
    textAlign: 'right',
    writingDirection: 'rtl',
  },
});

export default FarmCard;
