export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum TaskRecurrence {
  ONCE = 'once',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly'
}

export interface Task {
  id?: string;
  title: string;
  description: string;
  farmId: string;
  farmName: string;
  assignedTo: string;
  assigneeName: string;
  assignedBy: string;
  assignedByName: string;
  dueDate: number; // timestamp
  priority: TaskPriority;
  status: TaskStatus;
  createdAt: Date;
  updatedAt: Date;
  completed_at?: Date;
  notes?: string;
  attachments?: string[];
  related_animal_id?: string;
  related_animal_name?: string;
  
  // Recurrence fields
  recurrence: TaskRecurrence;
  parent_task_id?: string; // For tasks generated from a recurring task
}
