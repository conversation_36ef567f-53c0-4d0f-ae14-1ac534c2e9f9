import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useRouter } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import { formatDate } from '@/utils/date-utils';
import { uploadImage } from '@/services/storage-service';
import {
  User,
  Mail,
  Shield,
  Calendar,
  CheckCircle,
  XCircle,
  BarChart3,
  Building2,
  Users,
  Camera,
  Edit3,
  ArrowLeft,
} from 'lucide-react-native';

// Import Firebase functions to get user statistics
import { collection, query, where, getDocs } from 'firebase/firestore';
import { firestore } from '@/config/firebase';

interface UserStats {
  farmsOwned: number;
  farmsAssigned: number;
  totalAnimals: number;
}

export default function ProfileScreen() {
  const { user, updateProfile } = useAuthStore();
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const router = useRouter();
  const [stats, setStats] = useState<UserStats>({
    farmsOwned: 0,
    farmsAssigned: 0,
    totalAnimals: 0,
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const styles = getStyles(themedColors, language);

  useEffect(() => {
    if (user) {
      fetchUserStats();
    }
  }, [user]);

  const fetchUserStats = async () => {
    if (!user) return;

    try {
      setIsLoadingStats(true);
      
      // Get farms owned (for owners)
      let farmsOwned = 0;
      if (user.role === 'owner') {
        const farmsQuery = query(
          collection(firestore, 'farms'),
          where('ownerId', '==', user.id)
        );
        const farmsSnapshot = await getDocs(farmsQuery);
        farmsOwned = farmsSnapshot.size;
      }

      // Get farms assigned (for admin/caretaker)
      let farmsAssigned = 0;
      if (user.role === 'admin' || user.role === 'caretaker') {
        farmsAssigned = user.assignedFarmIds?.length || 0;
      }

      // Get total animals across all accessible farms
      let totalAnimals = 0;
      const farmIds = user.assignedFarmIds || [];

      if (farmIds.length > 0) {
        for (const farmId of farmIds) {
          const animalsQuery = query(
            collection(firestore, 'farms', farmId, 'animals')
          );
          const animalsSnapshot = await getDocs(animalsQuery);
          totalAnimals += animalsSnapshot.size;
        }
      }

      setStats({
        farmsOwned,
        farmsAssigned,
        totalAnimals,
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const handleProfilePicturePress = () => {
    Alert.alert(
      t('profile.selectImageSource'),
      '',
      [
        {
          text: t('profile.camera'),
          onPress: () => handleImagePick(true),
        },
        {
          text: t('profile.gallery'),
          onPress: () => handleImagePick(false),
        },
        ...(user?.profilePicture ? [{
          text: t('profile.removeProfilePicture'),
          onPress: handleRemoveProfilePicture,
          style: 'destructive' as const,
        }] : []),
        {
          text: t('common.cancel'),
          style: 'cancel' as const,
        },
      ]
    );
  };

  const handleImagePick = async (useCamera: boolean) => {
    try {
      let result;

      if (useCamera) {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(t('common.error'), 'Camera permission is required to take photos');
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });
      } else {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(t('common.error'), 'Media library permission is required to select photos');
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImageUri = result.assets[0].uri;
        await uploadProfilePicture(selectedImageUri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('common.error'), 'Failed to select image. Please try again.');
    }
  };

  const uploadProfilePicture = async (uri: string) => {
    if (!user) return;

    try {
      setIsUploadingImage(true);
      const timestamp = Date.now();
      const imagePath = `profile-pictures/${user.id}/${timestamp}.jpg`;

      const downloadURL = await uploadImage(uri, imagePath);
      await updateProfile({ profilePicture: downloadURL });

      Alert.alert(t('common.success'), 'Profile picture updated successfully');
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      Alert.alert(t('common.error'), 'Failed to update profile picture. Please try again.');
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleRemoveProfilePicture = async () => {
    if (!user) return;

    try {
      setIsUploadingImage(true);
      await updateProfile({ profilePicture: undefined });
      Alert.alert(t('common.success'), 'Profile picture removed successfully');
    } catch (error) {
      console.error('Error removing profile picture:', error);
      Alert.alert(t('common.error'), 'Failed to remove profile picture. Please try again.');
    } finally {
      setIsUploadingImage(false);
    }
  };

  if (!user) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themedColors.primary} />
          <Text style={styles.loadingText}>{t('common.loading')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  const memberSinceDate = user.createdAt;

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('profile.title'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold', color: themedColors.text },
        headerStyle: {
          backgroundColor: themedColors.background,
        },
        headerTintColor: themedColors.text,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Picture Section */}
        <View style={styles.profilePictureSection}>
          <TouchableOpacity
            style={styles.profilePictureContainer}
            onPress={handleProfilePicturePress}
            disabled={isUploadingImage}
            activeOpacity={0.7}
          >
            {user?.profilePicture ? (
              <Image source={{ uri: user.profilePicture }} style={styles.profilePicture} />
            ) : (
              <View style={styles.profilePicturePlaceholder}>
                <User size={40} color={themedColors.textSecondary} />
              </View>
            )}

            <View style={styles.profilePictureOverlay}>
              {isUploadingImage ? (
                <ActivityIndicator size="small" color={themedColors.background} />
              ) : (
                <Camera size={16} color={themedColors.background} />
              )}
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.editProfilePictureButton}
            onPress={handleProfilePicturePress}
            disabled={isUploadingImage}
            activeOpacity={0.7}
          >
            <Edit3 size={16} color={themedColors.primary} />
            <Text style={styles.editProfilePictureText}>
              {t('profile.changeProfilePicture')}
            </Text>
          </TouchableOpacity>


        </View>

        {/* Personal Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('profile.personalInfo')}</Text>
          
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <User size={20} color={themedColors.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>{t('profile.name')}</Text>
              <Text style={styles.infoValue}>{user.name || t('common.notAvailable')}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Mail size={20} color={themedColors.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>{t('profile.email')}</Text>
              <Text style={styles.infoValue}>{user.email}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Shield size={20} color={themedColors.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>{t('profile.role')}</Text>
              <Text style={styles.infoValue}>
                {t(`profile.roles.${user.role}` as any)}
              </Text>
            </View>
          </View>
        </View>

        {/* Account Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('profile.accountInfo')}</Text>
          
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Calendar size={20} color={themedColors.primary} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>{t('profile.memberSince')}</Text>
              <Text style={styles.infoValue}>
                {formatDate(memberSinceDate, language)}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              {user.emailVerified ? (
                <CheckCircle size={20} color={themedColors.success} />
              ) : (
                <XCircle size={20} color={themedColors.error} />
              )}
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>{t('profile.emailVerified')}</Text>
              <Text style={[
                styles.infoValue,
                { color: user.emailVerified ? themedColors.success : themedColors.error }
              ]}>
                {user.emailVerified ? t('profile.verified') : t('profile.notVerified')}
              </Text>
            </View>
          </View>
        </View>

        {/* Statistics Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('profile.statistics')}</Text>
          
          {isLoadingStats ? (
            <View style={styles.statsLoadingContainer}>
              <ActivityIndicator size="small" color={themedColors.primary} />
              <Text style={styles.statsLoadingText}>{t('common.loading')}</Text>
            </View>
          ) : (
            <>
              {user.role === 'owner' && (
                <View style={styles.infoItem}>
                  <View style={styles.infoIconContainer}>
                    <Building2 size={20} color={themedColors.primary} />
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.infoLabel}>{t('profile.farmsOwned')}</Text>
                    <Text style={styles.infoValue}>{stats.farmsOwned}</Text>
                  </View>
                </View>
              )}

              {(user.role === 'admin' || user.role === 'caretaker') && (
                <View style={styles.infoItem}>
                  <View style={styles.infoIconContainer}>
                    <Users size={20} color={themedColors.primary} />
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.infoLabel}>{t('profile.farmsAssigned')}</Text>
                    <Text style={styles.infoValue}>{stats.farmsAssigned}</Text>
                  </View>
                </View>
              )}

              <View style={styles.infoItem}>
                <View style={styles.infoIconContainer}>
                  <BarChart3 size={20} color={themedColors.primary} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>{t('profile.totalAnimals')}</Text>
                  <Text style={styles.infoValue}>{stats.totalAnimals}</Text>
                </View>
              </View>
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  profilePictureSection: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 16,
  },
  profilePictureContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profilePicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: themedColors.primary,
  },
  profilePicturePlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: themedColors.card,
    borderWidth: 4,
    borderColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profilePictureOverlay: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: themedColors.background,
  },
  editProfilePictureButton: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: themedColors.primaryLight,
  },
  editProfilePictureText: {
    fontSize: 14,
    color: themedColors.primary,
    fontWeight: '500',
    marginLeft: language === 'ur' ? 0 : 8,
    marginRight: language === 'ur' ? 8 : 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: themedColors.textSecondary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 16,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  infoItem: {
    flexDirection: language === 'ur' ? 'row-reverse' : 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: themedColors.card,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  infoIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: language === 'ur' ? 0 : 16,
    marginLeft: language === 'ur' ? 16 : 0,
  },
  infoContent: {
    flex: 1,
    alignItems: language === 'ur' ? 'flex-end' : 'flex-start',
  },
  infoLabel: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 4,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  statsLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  statsLoadingText: {
    marginLeft: 12,
    fontSize: 14,
    color: themedColors.textSecondary,
  },
});
