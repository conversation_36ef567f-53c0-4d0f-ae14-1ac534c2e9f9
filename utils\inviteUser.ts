import { createUserWithEmailAndPassword, sendPasswordResetEmail, sendEmailVerification, signOut, signInWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { auth, firestore, actionCodeSettings } from '@/config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Invite a new user to the system with multi-tenancy support
 *
 * @param email Email address of the user to invite
 * @param name Name of the user to invite
 * @param role Role of the user (admin or caretaker)
 * @param farmIds Array of farm IDs to assign the user to
 * @param inviterId ID of the user sending the invitation
 * @returns Object containing the new user ID and temporary password
 */
export async function inviteUser(
  email: string,
  name: string,
  role: 'admin' | 'caretaker',
  farmIds: string[],
  inviterId: string
) {
  // Validate inputs
  if (!email || !name || !role || !farmIds.length || !inviterId) {
    throw new Error('Missing required fields');
  }

  // Check if the inviter exists and has permission
  const inviterRef = doc(firestore, 'users', inviterId);
  const inviterDoc = await getDoc(inviterRef);

  if (!inviterDoc.exists()) {
    throw new Error('Inviter not found');
  }

  const inviterData = inviterDoc.data();

  // Check if inviter has permission to invite this role
  if (role === 'admin' && inviterData.role !== 'owner') {
    throw new Error('Only owners can invite admins');
  }

  // Verify that the inviter has access to all the farms
  for (const farmId of farmIds) {
    const farmRef = doc(firestore, 'farms', farmId);
    const farmDoc = await getDoc(farmRef);

    if (!farmDoc.exists()) {
      throw new Error(`Farm ${farmId} not found`);
    }

    const farmData = farmDoc.data();

    if (inviterData.role === 'owner') {
      // Owners can only invite to their own farms
      if (farmData.ownerId !== inviterId) {
        throw new Error(`You don't have permission to invite users to farm ${farmId}`);
      }
    } else if (inviterData.role === 'admin') {
      // Admins can only invite to farms they're assigned to
      if (!inviterData.assignedFarmIds || !inviterData.assignedFarmIds.includes(farmId)) {
        throw new Error(`You don't have permission to invite users to farm ${farmId}`);
      }
    }
  }

  let tempPassword: string;
  let userCredential;

  try {
    // Different password approaches based on role
    if (role === 'admin') {
      // For admins, generate a random password and they'll reset it via email
      // Ensure it's at least 8 characters long
      tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-2);
      userCredential = await createUserWithEmailAndPassword(auth, email, tempPassword);
    } else if (role === 'caretaker') {
      // For caretakers (who may be illiterate), create a simple password using name + CNIC
      // Get the user document to access CNIC
      const inviterRef = doc(firestore, 'users', inviterId);
      const inviterDoc = await getDoc(inviterRef);

      if (!inviterDoc.exists()) {
        throw new Error('Inviter not found');
      }

      // Get CNIC from the staff collection using the email
      const staffQuery = query(
        collection(firestore, 'staff'),
        where('email', '==', email)
      );

      const staffSnapshot = await getDocs(staffQuery);
      let cnic = '';

      if (!staffSnapshot.empty) {
        const staffData = staffSnapshot.docs[0].data();
        cnic = staffData.cnic || '';
      }

      // If no CNIC found, use a random number
      if (!cnic) {
        cnic = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      }

      // Create password from first 4 letters of name + 3 digits from CNIC
      // Ensure the password is at least 6 characters long to meet Firebase requirements
      let namePrefix = name.substring(0, 4).toLowerCase();
      // If name is too short, pad it with 'x' characters
      while (namePrefix.length < 4) {
        namePrefix += 'x';
      }

      // Extract digits from CNIC and take 3 digits
      let cnicDigits = cnic.replace(/\D/g, '').substring(0, 3);
      // If CNIC doesn't have enough digits, pad with random numbers
      while (cnicDigits.length < 3) {
        cnicDigits += Math.floor(Math.random() * 10).toString();
      }

      tempPassword = namePrefix + cnicDigits; // This will always be at least 7 characters

      userCredential = await createUserWithEmailAndPassword(auth, email, tempPassword);
    } else {
      throw new Error('Invalid role specified');
    }

    const newUser = userCredential.user;

    // Get the tenant ID (owner ID) for each farm
    const tenantIds = new Set<string>();

    for (const farmId of farmIds) {
      const farmRef = doc(firestore, 'farms', farmId);
      const farmDoc = await getDoc(farmRef);

      if (farmDoc.exists()) {
        const farmData = farmDoc.data();
        tenantIds.add(farmData.ownerId);
      }
    }

    // Create user document in Firestore
    const timestamp = Date.now();
    await setDoc(doc(firestore, 'users', newUser.uid), {
      id: newUser.uid,
      email: email,
      name: name,
      role: role,
      language: 'en',
      preferAudio: false,
      offlineMode: false,
      emailVerified: role === 'caretaker', // Caretakers don't need to verify email
      createdAt: timestamp,
      updatedAt: timestamp,
      assignedFarmIds: farmIds,
      tenantId: Array.from(tenantIds)[0], // Primary tenant ID (first farm's owner)
      tempPassword: tempPassword, // Store the password for reference
      isEmployee: true, // Flag to identify this user as an employee
      // Add default employee fields
      phone_number: '',
      cnic: cnic || '',
      age: 0,
      gender: '',
      joining_date: new Date(),
      status: 'active'
    });

    // Note: Staff is now managed through Users collection only
    // assignedFarmIds are stored in the user document

    // Send verification email for all users
    try {
      // 1. Send email verification with action code settings
      await sendEmailVerification(newUser, actionCodeSettings);

      // 2. For admins, also send password reset email
      if (role === 'admin') {
        try {
          // Force a password reset email to be sent with extended expiration and action code settings
          await sendPasswordResetEmail(auth, email, {
            // This makes the reset link valid for 1 week instead of the default 1 hour
            expires: 60 * 60 * 24 * 7, // 7 days in seconds
            ...actionCodeSettings
          });
        } catch (resetError) {
          console.error('Error sending password reset email:', resetError);
          // Continue execution - we'll return the tempPassword for manual sharing
        }
      }
    } catch (emailError) {
      console.error('Error sending verification email:', emailError);
      // Continue execution - we'll return the tempPassword for manual sharing
    }

    // Store the current user's email to restore session
    const inviterEmail = await getDoc(doc(firestore, 'users', inviterId)).then(doc => doc.data()?.email);
    const inviterPassword = await AsyncStorage.getItem('ownerPassword');

    // Try to restore the original user's session
    if (inviterEmail && inviterPassword) {
      try {

        // Sign out from the newly created user account
        await signOut(auth);

        // Sign back in as the original user
        await signInWithEmailAndPassword(auth, inviterEmail, inviterPassword);

        // Store a flag indicating we've restored the session
        await AsyncStorage.setItem('sessionRestored', 'true');
      } catch (signInError) {
        console.error('Error restoring inviter session:', signInError);
        // Continue execution even if session restoration fails
      }
    } else {
      console.warn('Could not restore original user session - missing email or password');
      if (!inviterEmail) console.warn('Missing inviter email');
      if (!inviterPassword) console.warn('Missing inviter password');
    }

    return {
      userId: newUser.uid,
      tempPassword
    };
  } catch (error) {
    console.error('Error inviting user:', error);

    // Check for specific error codes - handle different possible formats
    if (error.code === 'auth/email-already-in-use' ||
        (error.message && error.message.includes('email-already-in-use')) ||
        (error.toString().includes('email-already-in-use'))) {

      throw new Error(`The email ${email} is already registered. Please use a different email address.`);
    }

    throw error;
  }
}
