import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { Farm, FarmStatus } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import { Save, MapPin, Building2, Camera, FileText, Mic, MicOff, Ruler } from 'lucide-react-native';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { useToast } from '@/contexts/ToastContext';
import LoadingIndicator from '@/components/LoadingIndicator';
import * as ImagePicker from 'expo-image-picker';
import ImageCaptureButtons from '@/components/ImageCaptureButtons';
import GenericDropdown from '@/components/GenericDropdown';
import { FARM_SIZE_UNITS, FARM_TYPES } from '@/constants/farm-constants';
import { startSpeechRecognition, stopSpeechRecognition, setCurrentField } from '@/services/speech-service';
import { getStoredLanguage, getSpeechLanguageCode } from '@/services/language-service';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';

export default function EditFarmScreen() {
  const { t, language } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { user } = useAuthStore();
  const { getFarm, updateFarm, isLoading } = useFarmStore();
  const { showToast } = useToast();
  const themedColors = useThemeColors();

  const { playSound } = useAudioFeedback();

  const [farm, setFarm] = useState<Farm | undefined>(getFarm(id));
  const [name, setName] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState<FarmStatus>(FarmStatus.ACTIVE);
  const [photoURL, setPhotoURL] = useState('');
  const [farmType, setFarmType] = useState('livestock');
  const [size, setSize] = useState('');
  const [sizeUnit, setSizeUnit] = useState('marla');
  const [description, setDescription] = useState('');

  // Voice recording states
  const [isRecordingDescription, setIsRecordingDescription] = useState(false);
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false);

  const [errors, setErrors] = useState({
    name: '',
    location: '',
    size: '',
  });

  useEffect(() => {
    if (farm) {
      setName(farm.name);
      setLocation(typeof farm.location === 'string' ? farm.location : farm.location?.address || '');
      setStatus(farm.status);
      setPhotoURL(farm.photoURL || '');
      setFarmType(farm.type || 'livestock');
      setSize(farm.size?.toString() || '');
      setSizeUnit(farm.sizeUnit || 'marla');
      setDescription(farm.description || '');
    }
  }, [farm]);

  // Photo handling functions
  const handleTakePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setPhotoURL(result.assets[0].uri);
        playSound('camera');
      }
    } catch (error) {
      console.error('Error taking photo:', error);
    }
  };

  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setPhotoURL(result.assets[0].uri);
        playSound('camera');
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };

  // Voice recording functions
  const startRecordingDescription = async () => {
    try {
      setIsRecordingDescription(true);
      setCurrentField('description');
      const language = await getStoredLanguage();
      const speechLanguage = getSpeechLanguageCode(language);

      await startSpeechRecognition(speechLanguage, (text: string) => {
        setDescription(prev => prev + ' ' + text);
        setIsProcessingSpeech(false);
      });

      playSound('start_recording');
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      setIsRecordingDescription(false);
    }
  };

  const stopRecordingDescription = async () => {
    try {
      setIsRecordingDescription(false);
      setIsProcessingSpeech(true);
      await stopSpeechRecognition();
      playSound('stop_recording');
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
      setIsProcessingSpeech(false);
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: '',
      location: '',
      size: '',
    };

    if (!name.trim()) {
      newErrors.name = t('farms.nameRequired');
      isValid = false;
    }

    if (!location.trim()) {
      newErrors.location = t('farms.locationRequired');
      isValid = false;
    }

    if (size && isNaN(Number(size))) {
      newErrors.size = t('farms.sizeInvalid');
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    if (!user || !farm) return;

    try {
      await updateFarm(id, {
        name,
        location: {
          address: location,
          latitude: 0,
          longitude: 0,
        },
        status,
        photoURL,
        size: size ? Number(size) : undefined,
        sizeUnit,
        type: farmType,
        description,
      });

      showToast({
        type: 'success',
        title: t('common.success'),
        message: t('farms.updateSuccess'),
      });

      // Navigate back to the farm detail screen
      router.back();
    } catch (error) {
      console.error('Error updating farm:', error);
      showToast({
        type: 'error',
        title: t('common.error'),
        message: t('common.errorOccurred'),
      });
    }
  };

  if (!farm) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: t('farms.editFarm'),
          headerBackTitle: t('common.back'),
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Photo Upload */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('common.addPhoto')}</Text>
            </View>
            {photoURL ? (
              <View style={styles.imageContainer}>
                <Image source={{ uri: photoURL }} style={styles.farmImage} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => setPhotoURL('')}
                >
                  <Text style={styles.removeImageText}>×</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.imageUploadCard}>
                <View style={styles.imagePlaceholder}>
                  <Camera size={40} color={themedColors.textSecondary} />
                  <Text style={styles.imagePlaceholderText}>{t('farms.addPhoto')}</Text>
                </View>
                <ImageCaptureButtons
                  onTakePhoto={handleTakePhoto}
                  onChooseFromLibrary={handlePickImage}
                />
              </View>
            )}
          </View>

          {/* Farm Name */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.name')}*</Text>
            </View>
            <Input
              value={name}
              onChangeText={setName}
              placeholder={t('farms.namePlaceholder')}
              error={errors.name}
              leftIcon={<Building2 size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Farm Type */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.type')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('farms.selectType')}
              items={FARM_TYPES.map(type => ({
                id: type.id,
                label: type.label,
              }))}
              value={farmType}
              onSelect={setFarmType}
              modalTitle={t('farms.selectType')}
              searchPlaceholder={t('farms.searchTypes')}
            />
          </View>

          {/* Size */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.size')}</Text>
            </View>
            <Input
              value={size}
              onChangeText={setSize}
              placeholder={t('farms.sizePlaceholder')}
              error={errors.size}
              leftIcon={<Ruler size={20} color={themedColors.textSecondary} />}
              keyboardType="numeric"
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Size Unit */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.sizeUnit')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('farms.selectUnit')}
              items={FARM_SIZE_UNITS.map(unit => ({
                id: unit.id,
                label: unit.label,
              }))}
              value={sizeUnit}
              onSelect={setSizeUnit}
              modalTitle={t('farms.selectUnit')}
              searchPlaceholder={t('farms.searchUnits')}
            />
          </View>

          {/* Location */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.location')}*</Text>
            </View>
            <Input
              value={location}
              onChangeText={setLocation}
              placeholder={t('farms.locationPlaceholder')}
              error={errors.location}
              leftIcon={<MapPin size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Description with Voice */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('common.description')}
              </Text>
              {Platform.OS !== 'web' && (
                <TouchableOpacity
                  style={[
                    styles.voiceButtonHeader,
                    isRecordingDescription && styles.voiceButtonRecording
                  ]}
                  onPress={() => {
                    if (isRecordingDescription) {
                      stopRecordingDescription();
                    } else {
                      startRecordingDescription();
                    }
                  }}
                >
                  {isRecordingDescription ? (
                    <MicOff size={20} color={themedColors.error} />
                  ) : (
                    <Mic size={20} color={themedColors.primary} />
                  )}
                </TouchableOpacity>
              )}
            </View>
            <Input
              value={description}
              onChangeText={setDescription}
              placeholder={t('farms.descriptionPlaceholder')}
              multiline
              numberOfLines={4}
              style={[
                styles.textArea,
                language === 'ur' ? styles.urduText : null
              ]}
            />
            {isProcessingSpeech && (
              <View style={styles.speechProcessing}>
                <ActivityIndicator size="small" color={themedColors.primary} />
                <Text style={styles.speechProcessingText}>
                  {t('common.processingSpeech')}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.status')}</Text>
            </View>
            <View style={styles.statusButtons}>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.ACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.ACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.ACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusActive')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.INACTIVE && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.INACTIVE)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.INACTIVE && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusInactive')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.PENDING && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.PENDING)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.PENDING && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusPending')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  status === FarmStatus.COMPLETED && styles.statusButtonSelected
                ]}
                onPress={() => setStatus(FarmStatus.COMPLETED)}
              >
                <Text style={[
                  styles.statusButtonText,
                  status === FarmStatus.COMPLETED && styles.statusButtonTextSelected,
                  language === 'ur' ? styles.urduText : null
                ]}>
                  {t('farms.statusCompleted')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('common.save')}
              onPress={handleSave}
              isLoading={isLoading}
              leftIcon={<Save size={20} color="white" />}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
  },
  statusButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statusButton: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  statusButtonSelected: {
    backgroundColor: themedColors.primary,
    borderColor: themedColors.primary,
  },
  statusButtonText: {
    color: themedColors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  statusButtonTextSelected: {
    color: 'white',
  },
  buttonContainer: {
    marginTop: 24,
  },
  // Image upload styles
  imageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  farmImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    backgroundColor: themedColors.card,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: themedColors.error,
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  imageUploadCard: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: themedColors.border,
    borderStyle: 'dashed',
  },
  imagePlaceholder: {
    alignItems: 'center',
    marginBottom: 16,
  },
  imagePlaceholderText: {
    color: themedColors.textSecondary,
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  // Voice recording styles
  voiceButtonHeader: {
    backgroundColor: themedColors.card,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  voiceButtonRecording: {
    backgroundColor: themedColors.error + '20',
    borderColor: themedColors.error,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  speechProcessing: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 12,
  },
  speechProcessingText: {
    color: themedColors.textSecondary,
    fontSize: 14,
    marginLeft: 8,
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
});
