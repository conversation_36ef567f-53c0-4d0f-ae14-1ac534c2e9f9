// DEPRECATED: This script is no longer needed as staff management has been refactored.
// Staff counts are now calculated dynamically from the Users collection using assignedFarmIds.
// Farm documents no longer store staff arrays or staffCount fields.
//
// If you need to clean up existing farm documents, use the cleanup script instead.

console.log('This script is deprecated. Staff is now managed through Users collection only.');
console.log('Staff counts are calculated dynamically from assignedFarmIds in user documents.');

async function deprecatedFixStaffCount() {
  console.log('This function is deprecated and should not be used.');
  console.log('Staff management has been refactored to use Users collection only.');
}

// Run the deprecated function (does nothing)
deprecatedFixStaffCount();
