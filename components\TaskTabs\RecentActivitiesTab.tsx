import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  RefreshControl,
  TouchableOpacity
} from 'react-native';
import { Activity as ActivityIcon, Clock, User } from 'lucide-react-native';

import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { Activity } from '@/services/activity-service';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import { SafeAreaView } from 'react-native-safe-area-context';

interface RecentActivitiesTabProps {
  activities: Activity[];
  loading: boolean;
  refreshing: boolean;
  onRefresh: () => void;
}

export default function RecentActivitiesTab({
  activities,
  loading,
  refreshing,
  onRefresh
}: RecentActivitiesTabProps) {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();

  const styles = getStyles(themedColors, language);

  const renderActivityItem = ({ item }: { item: Activity }) => {
    // Handle different date formats from Firebase
    let formattedDate = 'Invalid Date';
    try {
      let dateObj: Date;

      if (item.createdAt instanceof Date) {
        dateObj = item.createdAt;
      } else if (typeof item.createdAt === 'string') {
        dateObj = new Date(item.createdAt);
      } else if (typeof item.createdAt === 'number') {
        dateObj = new Date(item.createdAt);
      } else if (item.createdAt && typeof item.createdAt === 'object' && 'seconds' in item.createdAt) {
        // Firebase Timestamp object
        dateObj = new Date((item.createdAt as any).seconds * 1000);
      } else {
        dateObj = new Date();
      }

      if (!isNaN(dateObj.getTime())) {
        formattedDate = dateObj.toLocaleDateString(language === 'ur' ? 'ur-PK' : 'en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      formattedDate = new Date().toLocaleDateString(language === 'ur' ? 'ur-PK' : 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }

    return (
      <TouchableOpacity style={styles.activityCard}>
        <View style={[styles.activityHeader, language === 'ur' && { flexDirection: 'row-reverse' }]}>
          <View style={[styles.activityIconContainer, language === 'ur' && { marginLeft: 12, marginRight: 0 }]}>
            <ActivityIcon size={20} color={themedColors.primary} />
          </View>
          <View style={[styles.activityContent, language === 'ur' && { alignItems: 'flex-end' }]}>
            <Text style={[styles.activityTitle, language === 'ur' && { textAlign: 'right' }]}>
              {item.title}
            </Text>
            <Text style={[styles.activityDescription, language === 'ur' && { textAlign: 'right' }]}>
              {item.description}
            </Text>
          </View>
        </View>

        <View style={[styles.activityFooter, language === 'ur' && { flexDirection: 'row-reverse' }]}>
          <View style={[styles.activityMeta, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            <Clock size={14} color={themedColors.textSecondary} />
            <Text style={[styles.activityMetaText, language === 'ur' && { marginRight: 4, marginLeft: 0 }]}>
              {formattedDate}
            </Text>
          </View>

          <View style={[styles.activityMeta, language === 'ur' && { flexDirection: 'row-reverse' }]}>
            <User size={14} color={themedColors.textSecondary} />
            <Text style={[styles.activityMetaText, language === 'ur' && { marginRight: 4, marginLeft: 0 }]}>
              {item.createdBy && item.createdBy !== 'unknown' ? item.createdBy : t('common.user', { defaultValue: 'User' })}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading && !refreshing) {
    <SafeAreaView style={[styles.tabContent, { backgroundColor: themedColors?.background }]} edges={['left', 'right']}>
    <LoadingIndicator message={t('activities.loading')} />;
  </SafeAreaView>
  }

  return (
    <View style={styles.tabContent}>
      {activities.length === 0 ? (
        <EmptyState
          icon={<ActivityIcon size={60} color={themedColors.primary} />}
          title={t('activities.noActivities')}
          message={t('activities.noActivitiesMessage')}
        />
      ) : (
        <FlatList
          data={activities}
          renderItem={renderActivityItem}
          keyExtractor={(item) => item.id!}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  tabContent: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  listContent: {
    padding: 16,
    paddingBottom: 100, // Extra padding for floating action button
  },
  activityCard: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
    shadowColor: themedColors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  activityIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: themedColors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 4,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  activityDescription: {
    fontSize: 14,
    color: themedColors.textSecondary,
    lineHeight: 20,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
  },
  activityMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityMetaText: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginLeft: 4,
  },
});
