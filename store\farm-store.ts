import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Farm, FarmStatus } from '@/types/farm';
import { collection, addDoc, updateDoc, deleteDoc, getDocs, doc, query, where, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';

interface FarmState {
  farms: Farm[];
  isLoading: boolean;
  error: string | null;
}

interface FarmStore extends FarmState {
  fetchFarms: (userId: string) => Promise<Farm[]>;
  getFarm: (id: string) => Farm | undefined;
  refreshFarm: (id: string) => Promise<Farm | undefined>;
  addFarm: (farm: Omit<Farm, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Farm>;
  updateFarm: (id: string, updates: Partial<Farm>) => Promise<Farm>;
  deleteFarm: (id: string) => Promise<void>;
  getFarmsByStatus: (status: FarmStatus) => Farm[];
  clearFarms: () => void;
}

export const useFarmStore = create<FarmStore>()(
  persist(
    (set, get) => ({
      farms: [],
      isLoading: false,
      error: null,

      fetchFarms: async (userId: string) => {
        set({ isLoading: true, error: null });
        try {
          // First, get the user's data to determine their role and assigned farms
          const userRef = doc(firestore, 'users', userId);
          const userDoc = await getDoc(userRef);

          if (!userDoc.exists()) {
            throw new Error('User not found');
          }

          const userData = userDoc.data();
          const userRole = userData.role;
          let fetchedFarms: Farm[] = [];

          // Get assigned farm IDs for all user roles
          const assignedFarmIds = userData.assignedFarmIds || [];

          if (userRole === 'owner') {
            // For owners, if no assignedFarmIds, fall back to querying by ownerId for backward compatibility
            if (assignedFarmIds.length > 0) {
              // Use assignedFarmIds if available
              const farmPromises = assignedFarmIds.map(async (farmId: string) => {
                const farmRef = doc(firestore, 'farms', farmId);
                const farmDoc = await getDoc(farmRef);
                if (farmDoc.exists()) {
                  return { id: farmDoc.id, ...farmDoc.data() } as Farm;
                }
                return null;
              });

              const farmResults = await Promise.all(farmPromises);
              fetchedFarms = farmResults.filter(farm => farm !== null) as Farm[];
            } else {
              // Fallback: fetch farms they own by ownerId
              const farmsRef = collection(firestore, 'farms');
              const q = query(farmsRef, where("ownerId", "==", userId));
              const querySnapshot = await getDocs(q);

              querySnapshot.forEach((doc) => {
                fetchedFarms.push({ id: doc.id, ...doc.data() } as Farm);
              });
            }
          } else if (userRole === 'admin' || userRole === 'caretaker') {
            // For employees, use assignedFarmIds
            if (assignedFarmIds.length > 0) {
              // Fetch each assigned farm
              const farmPromises = assignedFarmIds.map(async (farmId: string) => {
                const farmRef = doc(firestore, 'farms', farmId);
                const farmDoc = await getDoc(farmRef);
                if (farmDoc.exists()) {
                  return { id: farmDoc.id, ...farmDoc.data() } as Farm;
                }
                return null;
              });

              const farmResults = await Promise.all(farmPromises);
              fetchedFarms = farmResults.filter(farm => farm !== null) as Farm[];
            }
          }

          set({ farms: fetchedFarms, isLoading: false });
          return fetchedFarms;
        } catch (error) {
          console.error('Error fetching farms:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch farms',
            isLoading: false
          });
          return [];
        }
      },

      getFarm: (id: string) => {
        return get().farms.find(farm => farm.id === id);
      },

      refreshFarm: async (id: string) => {
        try {
          // Get the farm directly from Firestore to ensure we have the latest data
          const farmRef = doc(firestore, 'farms', id);
          const farmDoc = await getDoc(farmRef);

          if (farmDoc.exists()) {
            const farmData = { id: farmDoc.id, ...farmDoc.data() } as Farm;

            // Update the farm in the local state
            set(state => ({
              farms: state.farms.map(farm => farm.id === id ? farmData : farm)
            }));

            return farmData;
          }
          return undefined;
        } catch (error) {
          console.error('Error refreshing farm:', error);
          return get().getFarm(id);
        }
      },

      addFarm: async (farmData) => {
        set({ isLoading: true, error: null });
        try {
          // Create formatted date strings as per the new requirement
          const now = new Date();
          const formattedDate = now.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
          });

          const newFarmData = {
            ...farmData,
            createdAt: formattedDate,
            updatedAt: formattedDate,
          };


          // Add to Firestore
          const docRef = await addDoc(collection(firestore, 'farms'), newFarmData);

          // Create the complete farm object with the Firestore ID
          const newFarm: Farm = {
            id: docRef.id,
            ...newFarmData,
          } as Farm;

          // Update local state
          set(state => ({
            farms: [...state.farms, newFarm],
            isLoading: false,
          }));

          return newFarm;
        } catch (error) {
          console.error('Error adding farm:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to add farm',
            isLoading: false
          });
          throw error;
        }
      },

      updateFarm: async (id, updates) => {
        set({ isLoading: true, error: null });
        try {
          const farm = get().getFarm(id);
          if (!farm) {
            throw new Error('Farm not found');
          }

          // Create formatted date string for update
          const now = new Date();
          const formattedDate = now.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
          });

          const updatedData = {
            ...updates,
            updatedAt: formattedDate,
          };

          // Update in Firestore
          const farmRef = doc(firestore, 'farms', id);
          await updateDoc(farmRef, updatedData);

          const updatedFarm = {
            ...farm,
            ...updatedData,
          } as Farm;

          // Update local state
          set(state => ({
            farms: state.farms.map(farm =>
              farm.id === id ? updatedFarm : farm
            ),
            isLoading: false,
          }));

          return updatedFarm;
        } catch (error) {
          console.error('Error updating farm:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to update farm',
            isLoading: false
          });
          throw error;
        }
      },

      deleteFarm: async (id) => {
        set({ isLoading: true, error: null });
        try {
          // Delete from Firestore
          await deleteDoc(doc(firestore, 'farms', id));

          // Update local state
          set(state => ({
            farms: state.farms.filter(farm => farm.id !== id),
            isLoading: false,
          }));
        } catch (error) {
          console.error('Error deleting farm:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to delete farm',
            isLoading: false
          });
          throw error;
        }
      },

      getFarmsByStatus: (status: FarmStatus) => {
        return get().farms.filter(farm => farm.status === status);
      },

      clearFarms: () => {
        set({ farms: [], error: null });
      },
    }),
    {
      name: 'farm-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);


