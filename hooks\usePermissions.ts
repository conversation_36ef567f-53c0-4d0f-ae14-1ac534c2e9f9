import { useAuthStore } from '@/store/auth-store';

// Define permission types
export enum Permission {
  // Farm permissions
  CREATE_FARM = 'create_farm',
  EDIT_FARM = 'edit_farm',
  DELETE_FARM = 'delete_farm',
  VIEW_FARM = 'view_farm',

  // Animal permissions
  CREATE_ANIMAL = 'create_animal',
  EDIT_ANIMAL = 'edit_animal',
  DELETE_ANIMAL = 'delete_animal',
  VIEW_ANIMAL = 'view_animal',

  // Health record permissions
  CREATE_HEALTH_RECORD = 'create_health_record',
  EDIT_HEALTH_RECORD = 'edit_health_record',
  DELETE_HEALTH_RECORD = 'delete_health_record',
  VIEW_HEALTH_RECORD = 'view_health_record',

  // Staff permissions
  CREATE_STAFF = 'create_staff',
  EDIT_STAFF = 'edit_staff',
  DELETE_STAFF = 'delete_staff',
  VIEW_STAFF = 'view_staff',

  // User management permissions
  INVITE_ADMIN = 'invite_admin',
  INVITE_CARETAKER = 'invite_caretaker',

  // Task permissions
  CREATE_TASK = 'create_task',
  EDIT_TASK = 'edit_task',
  DELETE_TASK = 'delete_task',
  VIEW_TASK = 'view_task',
}

// Define role-based permissions
const rolePermissions: Record<string, Permission[]> = {
  owner: Object.values(Permission), // Owners have all permissions

  admin: [
    // Farm permissions (except delete)
    Permission.VIEW_FARM,
    Permission.EDIT_FARM,

    // Full animal permissions
    Permission.CREATE_ANIMAL,
    Permission.EDIT_ANIMAL,
    Permission.DELETE_ANIMAL,
    Permission.VIEW_ANIMAL,

    // Full health record permissions
    Permission.CREATE_HEALTH_RECORD,
    Permission.EDIT_HEALTH_RECORD,
    Permission.DELETE_HEALTH_RECORD,
    Permission.VIEW_HEALTH_RECORD,

    // Staff permissions (except delete)
    Permission.CREATE_STAFF,
    Permission.EDIT_STAFF,
    Permission.VIEW_STAFF,

    // Can invite caretakers but not admins
    Permission.INVITE_CARETAKER,

    // Full task permissions
    Permission.CREATE_TASK,
    Permission.EDIT_TASK,
    Permission.DELETE_TASK,
    Permission.VIEW_TASK,
  ],

  caretaker: [
    // View-only farm permissions
    Permission.VIEW_FARM,

    // Limited animal permissions
    Permission.VIEW_ANIMAL,

    // Limited health record permissions
    Permission.CREATE_HEALTH_RECORD,
    Permission.VIEW_HEALTH_RECORD,

    // View-only staff permissions
    Permission.VIEW_STAFF,

    // Limited task permissions
    Permission.VIEW_TASK,
  ],
};

/**
 * Hook to check if the current user has a specific permission
 */
export function usePermissions() {
  const { user } = useAuthStore();

  /**
   * Check if the current user has a specific permission
   * @param permission The permission to check
   * @param resourceOwnerId Optional owner ID of the resource being accessed
   * @returns Boolean indicating if the user has the permission
   */
  const hasPermission = (permission: Permission, resourceOwnerId?: string): boolean => {
    // If no user is logged in, deny all permissions
    if (!user) {
      return false;
    }

    // Get the user's role
    const role = user.role;

    // If the user is the owner of the resource, grant permission
    if (resourceOwnerId && user.id === resourceOwnerId) {
      return true;
    }

    // Check if the user's role has the requested permission
    return rolePermissions[role]?.includes(permission) || false;
  };

  /**
   * Check if the current user has any of the specified roles
   * @param roles Array of roles to check against
   * @returns Boolean indicating if the user has any of the roles
   */
  const hasRole = (roles: string[]): boolean => {
    if (!user) {
      return false;
    }

    return roles.includes(user.role);
  };

  return {
    hasPermission,
    hasRole,
  };
}
