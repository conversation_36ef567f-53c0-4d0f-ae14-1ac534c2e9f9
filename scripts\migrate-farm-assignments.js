/**
 * Migration script to consolidate ownedFarms and assignedFarms into assignedFarmIds
 * Run this script once to migrate existing user data
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, doc, updateDoc } from 'firebase/firestore';

// Your Firebase config
const firebaseConfig = {
  // Add your Firebase configuration here
};

const app = initializeApp(firebaseConfig);
const firestore = getFirestore(app);

async function migrateUserFarmAssignments() {
  try {
    console.log('Starting farm assignment migration...');
    
    const usersRef = collection(firestore, 'users');
    const usersSnapshot = await getDocs(usersRef);
    
    let migratedCount = 0;
    let skippedCount = 0;
    
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;
      
      // Skip if already has assignedFarmIds
      if (userData.assignedFarmIds && userData.assignedFarmIds.length > 0) {
        console.log(`User ${userId} already has assignedFarmIds, skipping...`);
        skippedCount++;
        continue;
      }
      
      let farmIds = [];
      
      // Collect farm IDs from various sources
      if (userData.role === 'owner' && userData.ownedFarms) {
        farmIds = [...userData.ownedFarms];
      } else if ((userData.role === 'admin' || userData.role === 'caretaker')) {
        if (userData.assignedFarms) {
          farmIds = [...userData.assignedFarms];
        } else if (userData.farmIds) {
          farmIds = [...userData.farmIds];
        }
      }
      
      // Update user document if we found farm IDs
      if (farmIds.length > 0) {
        await updateDoc(doc(firestore, 'users', userId), {
          assignedFarmIds: farmIds,
          updatedAt: Date.now()
        });
        
        console.log(`Migrated user ${userId} (${userData.role}): ${farmIds.length} farms`);
        migratedCount++;
      } else {
        console.log(`No farms found for user ${userId} (${userData.role})`);
        skippedCount++;
      }
    }
    
    console.log(`Migration completed!`);
    console.log(`- Migrated: ${migratedCount} users`);
    console.log(`- Skipped: ${skippedCount} users`);
    
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
migrateUserFarmAssignments();
