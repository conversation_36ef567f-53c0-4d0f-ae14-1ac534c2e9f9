import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  Alert,
  ActivityIndicator
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, User, MapPin, Check } from 'lucide-react-native';

import { colors } from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import StaffCard from '@/components/StaffCard';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import GenericDropdown from '@/components/GenericDropdown';
import { firestore } from '@/config/firebase';
import { collection, getDocs, doc, updateDoc, getDoc, arrayUnion } from 'firebase/firestore';

export default function AssignStaffScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { farms } = useFarmStore();
  
  const [staff, setStaff] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFarmId, setSelectedFarmId] = useState('');
  const [selectedFarmName, setSelectedFarmName] = useState('');
  const [assigningStaff, setAssigningStaff] = useState<Record<string, boolean>>({});
  
  useEffect(() => {
    loadStaff();
  }, []);
  
  const loadStaff = async () => {
    try {
      setLoading(true);
      
      // Get all staff from users collection
      const querySnapshot = await getDocs(collection(firestore, 'users'));
      const staffList: any[] = [];
      
      querySnapshot.forEach((doc) => {
        const userData = doc.data();
        staffList.push({
          id: doc.id,
          ...userData
        });
      });
      
      setStaff(staffList);
    } catch (error) {
      console.error('Error loading staff:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
    } finally {
      setLoading(false);
    }
  };
  
  const handleFarmSelect = (id: string) => {
    const selectedFarm = farms.find(farm => farm.id === id);
    if (selectedFarm) {
      setSelectedFarmId(selectedFarm.id);
      setSelectedFarmName(selectedFarm.name);
    }
  };
  
  const handleAssignStaff = async (staffId: string, staffName: string) => {
    if (!selectedFarmId) {
      Alert.alert(t('common.error'), t('farms.selectFarmFirst'));
      return;
    }
    
    try {
      setAssigningStaff(prev => ({ ...prev, [staffId]: true }));
      
      // Update the user's assignedFarmIds array only
      const userRef = doc(firestore, 'users', staffId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        const currentAssignedFarms = userData.assignedFarmIds || [];

        // Only add if not already assigned
        if (!currentAssignedFarms.includes(selectedFarmId)) {
          await updateDoc(userRef, {
            assignedFarmIds: arrayUnion(selectedFarmId),
            updatedAt: Date.now()
          });

          Alert.alert(
            t('common.success'),
            t('farms.staffAssigned', { staff: staffName, farm: selectedFarmName })
          );
        } else {
          Alert.alert(
            t('common.info'),
            t('farms.staffAlreadyAssigned')
          );
        }
      }
    } catch (error) {
      console.error('Error assigning staff:', error);
      Alert.alert(t('common.error'), t('common.errorOccurred'));
    } finally {
      setAssigningStaff(prev => ({ ...prev, [staffId]: false }));
    }
  };
  
  const handleAddStaff = () => {
    router.push('/employees/add');
  };
  
  const renderStaffItem = ({ item }: { item: any }) => {
    const isAssigning = assigningStaff[item.id] || false;
    
    return (
      <View style={styles.staffItemContainer}>
        <StaffCard 
          staff={item} 
          onPress={() => router.push(`/employees/${item.id}`)}
        />
        <TouchableOpacity
          style={[
            styles.assignButton,
            !selectedFarmId && styles.disabledButton
          ]}
          onPress={() => handleAssignStaff(item.id, item.name)}
          disabled={!selectedFarmId || isAssigning}
        >
          {isAssigning ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <>
              <Check size={16} color={colors.white} />
              <Text style={styles.assignButtonText}>{t('staff.assign')}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    );
  };
  
  if (loading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: t('staff.assignStaff'),
          headerBackTitle: t('common.back')
        }}
      />
      
      <View style={styles.content}>
        <View style={styles.farmSelectorContainer}>
          <Text style={styles.sectionTitle}>{t('staff.selectFarm')}</Text>
          <GenericDropdown
            placeholder={t('farms.selectFarm')}
            items={farms.map(farm => ({
              id: farm.id,
              label: farm.name,
              icon: <MapPin size={20} color={colors.primary} />
            }))}
            value={selectedFarmId}
            onSelect={handleFarmSelect}
            modalTitle={t('farms.selectFarm')}
          />
        </View>
        
        <View style={styles.staffListContainer}>
          <Text style={styles.sectionTitle}>{t('staff.availableStaff')}</Text>
          
          {staff.length === 0 ? (
            <EmptyState
              icon={<User size={60} color={colors.textLight} />}
              title={t('staff.noStaff')}
              message={t('staff.noStaffMessage')}
              actionLabel={t('staff.addStaff')}
              onAction={handleAddStaff}
            />
          ) : (
            <FlatList
              data={staff}
              renderItem={renderStaffItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContent}
            />
          )}
        </View>
      </View>
      
      {/* Add Staff Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={handleAddStaff}
      >
        <Plus size={24} color={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  farmSelectorContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  staffListContainer: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 80, // Space for the floating button
  },
  staffItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  assignButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  disabledButton: {
    backgroundColor: colors.textLight,
    opacity: 0.6,
  },
  assignButtonText: {
    color: colors.white,
    fontWeight: '500',
    marginLeft: 4,
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});
