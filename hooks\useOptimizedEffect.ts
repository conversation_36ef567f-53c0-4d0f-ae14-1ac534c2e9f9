/**
 * Optimized React hooks for better performance
 */

import { useEffect, useRef, useMemo, useCallback, DependencyList } from 'react';
import { startPerformanceTimer, endPerformanceTimer } from '@/utils/memory-utils';

/**
 * Deep comparison for dependency arrays to prevent unnecessary re-renders
 */
function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;
  
  if (a == null || b == null) return a === b;
  
  if (typeof a !== typeof b) return false;
  
  if (typeof a !== 'object') return a === b;
  
  if (Array.isArray(a) !== Array.isArray(b)) return false;
  
  if (Array.isArray(a)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false;
    }
    return true;
  }
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }
  
  return true;
}

/**
 * useEffect with deep comparison of dependencies
 * Prevents unnecessary re-runs when dependencies are objects/arrays with same content
 */
export function useDeepEffect(
  effect: React.EffectCallback,
  deps: DependencyList,
  debugLabel?: string
) {
  const prevDepsRef = useRef<DependencyList>();
  const hasChanged = !prevDepsRef.current || !deepEqual(prevDepsRef.current, deps);

  useEffect(() => {
    if (hasChanged) {
      if (debugLabel && __DEV__) {
        console.log(`[useDeepEffect] ${debugLabel} - dependencies changed, running effect`);
      }
      prevDepsRef.current = deps;
      return effect();
    }
  }, [hasChanged]);
}

/**
 * Optimized useEffect that tracks performance and prevents excessive calls
 */
export function useOptimizedEffect(
  effect: React.EffectCallback,
  deps: DependencyList,
  options: {
    debugLabel?: string;
    maxCallsPerSecond?: number;
    deepCompare?: boolean;
  } = {}
) {
  const { debugLabel, maxCallsPerSecond = 10, deepCompare = false } = options;
  const lastCallRef = useRef<number>(0);
  const callCountRef = useRef<number>(0);
  const resetTimeRef = useRef<number>(Date.now());

  const throttledEffect = useCallback(() => {
    const now = Date.now();
    
    // Reset call count every second
    if (now - resetTimeRef.current >= 1000) {
      callCountRef.current = 0;
      resetTimeRef.current = now;
    }
    
    // Check if we're exceeding the call limit
    if (callCountRef.current >= maxCallsPerSecond) {
      if (debugLabel && __DEV__) {
        console.warn(`[useOptimizedEffect] ${debugLabel} - throttled due to excessive calls`);
      }
      return;
    }
    
    callCountRef.current++;
    lastCallRef.current = now;
    
    if (debugLabel && __DEV__) {
      startPerformanceTimer(`OptimizedEffect_${debugLabel}`);
    }
    
    const cleanup = effect();
    
    if (debugLabel && __DEV__) {
      endPerformanceTimer(`OptimizedEffect_${debugLabel}`);
    }
    
    return cleanup;
  }, [effect, debugLabel, maxCallsPerSecond]);

  if (deepCompare) {
    useDeepEffect(throttledEffect, deps, debugLabel);
  } else {
    useEffect(throttledEffect, deps);
  }
}

/**
 * Memoized callback with deep comparison
 */
export function useDeepCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: DependencyList
): T {
  const prevDepsRef = useRef<DependencyList>();
  const callbackRef = useRef<T>(callback);

  const hasChanged = !prevDepsRef.current || !deepEqual(prevDepsRef.current, deps);

  if (hasChanged) {
    prevDepsRef.current = deps;
    callbackRef.current = callback;
  }

  return callbackRef.current;
}

/**
 * Memoized value with deep comparison
 */
export function useDeepMemo<T>(
  factory: () => T,
  deps: DependencyList
): T {
  const prevDepsRef = useRef<DependencyList>();
  const valueRef = useRef<T>();

  const hasChanged = !prevDepsRef.current || !deepEqual(prevDepsRef.current, deps);

  if (hasChanged || valueRef.current === undefined) {
    prevDepsRef.current = deps;
    valueRef.current = factory();
  }

  return valueRef.current!;
}

/**
 * Debounced effect hook
 */
export function useDebouncedEffect(
  effect: React.EffectCallback,
  deps: DependencyList,
  delay: number = 300,
  debugLabel?: string
) {
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (debugLabel && __DEV__) {
        console.log(`[useDebouncedEffect] ${debugLabel} - running debounced effect`);
      }
      effect();
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, deps);
}

/**
 * Hook to track component re-renders for debugging
 */
export function useRenderTracker(componentName: string, props?: Record<string, any>) {
  const renderCountRef = useRef(0);
  const prevPropsRef = useRef(props);

  renderCountRef.current++;

  if (__DEV__) {
    console.log(`[RenderTracker] ${componentName} rendered ${renderCountRef.current} times`);
    
    if (props && prevPropsRef.current) {
      const changedProps: string[] = [];
      Object.keys(props).forEach(key => {
        if (props[key] !== prevPropsRef.current![key]) {
          changedProps.push(key);
        }
      });
      
      if (changedProps.length > 0) {
        console.log(`[RenderTracker] ${componentName} - changed props:`, changedProps);
      }
    }
    
    prevPropsRef.current = props;
  }

  return renderCountRef.current;
}

/**
 * Hook to prevent unnecessary re-renders by stabilizing object/array references
 */
export function useStableReference<T>(value: T): T {
  const ref = useRef<T>(value);
  
  if (!deepEqual(ref.current, value)) {
    ref.current = value;
  }
  
  return ref.current;
}

/**
 * Hook for conditional effects that only run when specific conditions are met
 */
export function useConditionalEffect(
  effect: React.EffectCallback,
  deps: DependencyList,
  condition: boolean,
  debugLabel?: string
) {
  useEffect(() => {
    if (condition) {
      if (debugLabel && __DEV__) {
        console.log(`[useConditionalEffect] ${debugLabel} - condition met, running effect`);
      }
      return effect();
    } else if (debugLabel && __DEV__) {
      console.log(`[useConditionalEffect] ${debugLabel} - condition not met, skipping effect`);
    }
  }, [...deps, condition]);
}

/**
 * Performance monitoring hook for components
 */
export function usePerformanceMonitor(componentName: string) {
  const mountTimeRef = useRef<number>(Date.now());
  const renderCountRef = useRef<number>(0);
  const lastRenderTimeRef = useRef<number>(Date.now());

  renderCountRef.current++;
  const currentRenderTime = Date.now();
  const timeSinceLastRender = currentRenderTime - lastRenderTimeRef.current;
  lastRenderTimeRef.current = currentRenderTime;

  useEffect(() => {
    const mountTime = Date.now() - mountTimeRef.current;
    if (__DEV__) {
      console.log(`[PerformanceMonitor] ${componentName} mounted in ${mountTime}ms`);
    }
  }, []);

  if (__DEV__ && renderCountRef.current > 1) {
    if (timeSinceLastRender < 16) { // Less than one frame (60fps)
      console.warn(`[PerformanceMonitor] ${componentName} - rapid re-render detected (${timeSinceLastRender}ms)`);
    }
  }

  return {
    renderCount: renderCountRef.current,
    timeSinceMount: currentRenderTime - mountTimeRef.current,
    timeSinceLastRender
  };
}
