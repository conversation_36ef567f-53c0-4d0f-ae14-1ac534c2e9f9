import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { useAuthStore } from '@/store/auth-store';

import { usePermissions, Permission } from '@/hooks/usePermissions';
import { RoleBasedContent } from './RoleBasedContent';
import { Shield, User as UserIcon, Mail } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { colors } from '@/constants/colors';
import { inviteUser } from '@/utils/inviteUser';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { firestore } from '@/config/firebase';

/**
 * Component for inviting new users to the app
 */
export function UserInvitation() {
  const { user } = useAuthStore();

  const { hasPermission } = usePermissions();
  const { t, language } = useTranslation();

  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [role, setRole] = useState<'admin' | 'caretaker'>('caretaker');

  const [loading, setLoading] = useState(false);



  // Check if email is already in use
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      // Check in users collection
      const usersQuery = query(
        collection(firestore, 'users'),
        where('email', '==', email)
      );

      const usersSnapshot = await getDocs(usersQuery);

      return !usersSnapshot.empty;
    } catch (error) {
      console.error('Error checking email existence:', error);
      return false;
    }
  };

  // Handle user invitation
  const handleInviteUser = async () => {
    // Validate inputs
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter an email address');
      return;
    }

    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name');
      return;
    }

    if (role === 'admin' && user?.role !== 'owner') {
      Alert.alert('Permission Denied', 'Only owners can invite admins');
      return;
    }



    setLoading(true);

    try {
      if (!user) {
        throw new Error('You must be logged in to invite users');
      }

      // Check if email already exists
      const emailExists = await checkEmailExists(email);
      if (emailExists) {
        Alert.alert(
          'Email Already in Use',
          `The email ${email} is already registered. Please use a different email address.`
        );
        setLoading(false);
        return;
      }

      // Use the inviteUser utility function
      const result = await inviteUser(
        email,
        name,
        role,
        [],
        user.id
      );

      // Clear form
      setEmail('');
      setName('');
      setRole('caretaker');


      // Different success messages based on role
      if (role === 'admin') {
        Alert.alert(
          'Admin Invited Successfully',
          `Emails have been sent to ${email} with:\n- Verification link\n- Password reset link\n\nPlease ask them to check their inbox and spam folder.\n\nTemporary password: ${result.tempPassword}`,
          [
            {
              text: 'Copy Password',
              onPress: async () => {
                await Clipboard.setStringAsync(result.tempPassword);
                Alert.alert('Copied', 'Password copied to clipboard');
              }
            },
            { text: 'OK' }
          ]
        );
      } else {
        Alert.alert(
          'Worker Invited Successfully',
          `A verification email has been sent to ${email}. Please ask them to check their inbox and spam folder.\n\nLogin credentials:\nEmail: ${email}\nPassword: ${result.tempPassword}`,
          [
            {
              text: 'Copy Credentials',
              onPress: async () => {
                const credentials = `Email: ${email}\nPassword: ${result.tempPassword}`;
                await Clipboard.setStringAsync(credentials);
                Alert.alert('Copied', 'Credentials copied to clipboard');
              }
            },
            { text: 'OK' }
          ]
        );
      }
    } catch (error: any) {
      // Check for specific error messages
      if (error.message && error.message.includes('already registered')) {
        Alert.alert('Email Already in Use', error.message);
      } else if (error.toString().includes('email-already-in-use')) {
        Alert.alert('Email Already in Use', `The email ${email} is already registered. Please use a different email address.`);
      } else {
        Alert.alert('Error', error.message || 'Failed to invite user');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <RoleBasedContent allowedRoles={['owner', 'admin']}>
        <Text style={styles.title}>{t('settings.inviteUser')}</Text>

        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('auth.email')}*</Text>
          <TextInput
            style={styles.input}
            placeholder="<EMAIL>"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>{t('auth.name')}*</Text>
          <TextInput
            style={styles.input}
            placeholder="Full Name"
            value={name}
            onChangeText={setName}
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Role*</Text>
          <View style={styles.roleContainer}>
            <RoleBasedContent allowedRoles={['owner']}>
              <TouchableOpacity
                style={[styles.roleButton, role === 'admin' && styles.roleButtonSelected]}
                onPress={() => setRole('admin')}
              >
                <Shield
                  size={20}
                  color={role === 'admin' ? colors.primary : colors.textSecondary}
                  style={styles.roleIcon}
                />
                <Text style={[styles.roleText, role === 'admin' && styles.roleTextSelected]}>
                  Admin (Manager)
                </Text>
              </TouchableOpacity>
            </RoleBasedContent>

            <TouchableOpacity
              style={[styles.roleButton, role === 'caretaker' && styles.roleButtonSelected]}
              onPress={() => setRole('caretaker')}
            >
              <UserIcon
                size={20}
                color={role === 'caretaker' ? colors.primary : colors.textSecondary}
                style={styles.roleIcon}
              />
              <Text style={[styles.roleText, role === 'caretaker' && styles.roleTextSelected]}>
                Caretaker
              </Text>
            </TouchableOpacity>
          </View>
        </View>



        <TouchableOpacity
          style={[
            styles.inviteButton,
            loading && styles.inviteButtonDisabled
          ]}
          onPress={handleInviteUser}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <Mail size={20} color="#fff" style={styles.inviteIcon} />
              <Text style={styles.inviteButtonText}>{t('settings.sendInvite')}</Text>
            </>
          )}
        </TouchableOpacity>

        <Text style={styles.inviteNote}>
          {role === 'admin' ?
            'A verification email and password reset link will be sent to the admin. You will also receive their temporary password to share as a backup.' :
            'A verification email will be sent to the caretaker. You will also receive their login credentials to share as a backup.'}
        </Text>
      </RoleBasedContent>

      <RoleBasedContent allowedRoles={['caretaker']}>
        <View style={styles.permissionDenied}>
          <Text style={styles.permissionDeniedText}>
            {t('common.permissionDenied')}
          </Text>
          <Text style={styles.permissionDeniedSubtext}>
            You do not have permission to invite users.
          </Text>
        </View>
      </RoleBasedContent>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: colors.text,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: colors.text,
  },
  input: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 12,
    color: colors.text,
  },
  roleContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  roleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: colors.card,
    marginRight: 12,
    flex: 1,
  },
  roleButtonSelected: {
    backgroundColor: colors.primaryLight,
  },
  roleIcon: {
    marginRight: 8,
  },
  roleText: {
    color: colors.textSecondary,
    fontSize: 14,
  },
  roleTextSelected: {
    color: colors.primary,
    fontWeight: '500',
  },

  inviteButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  inviteButtonDisabled: {
    backgroundColor: colors.inactive,
  },
  inviteIcon: {
    marginRight: 8,
  },
  inviteButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  inviteNote: {
    marginTop: 12,
    color: colors.textSecondary,
    fontSize: 12,
    textAlign: 'center',
  },
  permissionDenied: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionDeniedText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.error,
    marginBottom: 8,
  },
  permissionDeniedSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});
