export const ur = {
  common: {
    "caretaker": "نگہبان",
    "noFarmsTitle": "کوئی فارم دستیاب نہیں",
    "noFarmsMessage": "اس فیچر کا استعمال جاری رکھنے کے لیے براہ کرم ایک فارم شامل کریں۔",
    loading: "لوڈ ہو رہا ہے...",
    cancel: "منسوخ کریں",
    save: "محفوظ کریں",
    delete: "حذف کریں",
    edit: "ترمیم کریں",
    yes: "ہاں",
    no: "نہیں",
    ok: "ٹھیک ہے",
    confirm: "تصدیق کریں",
    back: "واپس",
    next: "اگلا",
    done: "ہو گیا",
    user: "صارف",
    search: "تلاش کریں",
    filter: "فلٹر کریں",
    all: "تمام",
    allFarms: "تمام فارمز",
    user: "صارف",
    error: "خرابی",
    success: "کامیابی",
    warning: "انتباہ",
    info: "معلومات",
    permissionDenied: "اجازت نہیں ملی",
    dismiss: "برخاست کریں",
    pageNotFound: "یہ سکرین موجود نہیں ہے۔",
    goToHome: "ہوم سکرین پر جائیں!",
    somethingWentWrong: "کچھ غلط ہو گیا",
    checkLogs: "مزید تفصیلات کے لیے اپنے آلے کے لاگز چیک کریں۔",
    selectDate: "تاریخ منتخب کریں",
    validationError: "تصدیق کی غلطی",
    fillRequiredFields: "براہ کرم تمام ضروری فیلڈز کو درست طریقے سے پر کریں۔",
    viewAll: "سب دیکھیں",
    filters: "فلٹرز",
    activeFilters: "فعال فلٹرز",
    clear: "صاف کریں",
    date: "تاریخ",
    month: "مہینہ",
    year: "سال",
    selectMonth: "مہینہ منتخب کریں",
    selectYear: "سال منتخب کریں",
    unknown: "نامعلوم",
    kg: "کلوگرام",
    goBack: "واپس جائیں",
    weekdays: {
      sun: "اتوار",
      mon: "پیر",
      tue: "منگل",
      wed: "بدھ",
      thu: "جمعرات",
      fri: "جمعہ",
      sat: "ہفتہ"
    },
    addPhoto:"تصویر شامل کریں",
    active: "فعال",
    inactive: "غیر فعال",
    notLoggedIn: "اس کارروائی کو انجام دینے کے لیے آپ کو لاگ ان ہونا چاہیے",
    permissionRequired: "اجازت درکار ہے",
    cameraPermission: "تصاویر لینے کے لیے کیمرہ کی اجازت درکار ہے",
    galleryPermission: "تصاویر منتخب کرنے کے لیے گیلری کی اجازت درکار ہے",
    networkError: "نیٹ ورک میں خرابی۔ براہ کرم اپنا کنکشن چیک کریں اور دوبارہ کوشش کریں",
    serverError: "سرور میں خرابی۔ براہ کرم بعد میں دوبارہ کوشش کریں",
    unknownError: "ایک نامعلوم خرابی پیش آئی۔ براہ کرم دوبارہ کوشش کریں",
    timeoutError: "درخواست کا وقت ختم ہو گیا۔ براہ کرم دوبارہ کوشش کریں",
    authError: "تصدیق میں خرابی۔ براہ کرم دوبارہ لاگ ان کریں",
    dataLoadError: "ڈیٹا لوڈ کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں",
    dataSaveError: "ڈیٹا محفوظ کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں",
    dataDeleteError: "ڈیٹا حذف کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں",
    invalidInput: "غلط ان پٹ۔ براہ کرم اپنی اندراجات چیک کریں",
    optional: "اختیاری",
    required: "درکار",
    retry: "دوبارہ کوشش کریں",
    recordNotFound: "ریکارڈ نہیں ملا",
    tryAgain: "دوبارہ کوشش کریں",
    continue: "جاری رکھیں",
    description: "تفصیل",
    created: "تخلیق کیا گیا",
    updated: "اپڈیٹ کیا گیا",
    add: "شامل کریں",
    selectCategory: "زمرہ منتخب کریں",
    searchCategories: "زمرے تلاش کریں",
    errorOccurred: "ایک خرابی پیش آگئی",
    microphonePermissionDenied: "آڈیو ریکارڈ کرنے کے لیے مائیکروفون کی اجازت درکار ہے",
    recordingSaved: "ریکارڈنگ کامیابی سے محفوظ ہوگئی",
    speechRecognitionError: "تقریر کو متن میں تبدیل کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔",
    unsupported: "فیچر دستیاب نہیں",
    voiceRecordingWebUnsupported: "ویب براؤزر میں وائس ریکارڈنگ دستیاب نہیں ہے۔ براہ کرم اس فیچر کے لیے موبائل ایپ استعمال کریں۔",
    joiningDate: "شمولیت کی تاریخ",
    createdAt: "تخلیق کی تاریخ",
    lastUpdated: "آخری تازہ کاری",
    deactivate: "غیرفعال کریں",
    male: "مرد",
    female: "عورت",
    years: "سال",
    apply: 'لاگو کریں',
    searchStaff: 'ملازمین تلاش کریں',
    recording: "ریکارڈنگ جاری ہے...",
    recordingStarted: "آواز کی ریکارڈنگ شروع ہوگئی",
    recordingStopped: "آواز کی ریکارڈنگ بند ہوگئی",
    recordingFailed: "ریکارڈنگ شروع کرنے میں ناکام",
    close: "بند کریں",
    tryDifferentSearch: "مختلف تلاش کی اصطلاح یا فلٹر آزمائیں",
    notAvailable: "دستیاب نہیں",
    animal: "جانور",
    update: "اپ ڈیٹ کریں",
    updating: "اپ ڈیٹ ہو رہا ہے...",
    saving: "محفوظ کر رہا ہے...",
    noAddress: "کوئی پتہ نہیں"

  },

  auth: {
    login: "لاگ ان کریں",
    register: "رجسٹر کریں",
    forgotPassword: "پاسورڈ بھول گئے",
    email: "ای میل",
    password: "پاسورڈ",
    confirmPassword: "پاسورڈ کی تصدیق کریں",
    name: "نام",
    phone: "فون",
    loginWithGoogle: "گوگل کے ساتھ لاگ ان کریں",
    loginWithOTP: "OTP کے ساتھ لاگ ان کریں",
    dontHaveAccount: "اکاؤنٹ نہیں ہے؟",
    alreadyHaveAccount: "پہلے سے ہی اکاؤنٹ ہے؟",
    signUp: "سائن اپ کریں",
    signIn: "سائن ان کریں",
    enterOTP: "OTP درج کریں",
    verifyOTP: "OTP کی تصدیق کریں",
    resendOTP: "OTP دوبارہ بھیجیں",
    otpSent: "تصدیقی ای میل آپ کے ان باکس میں بھیج دی گئی ہے",
    invalidOTP: "غلط OTP",
    invalidCredentials: "غلط ای میل یا پاسورڈ",
    passwordsDontMatch: "پاسورڈ میچ نہیں کرتے",
    accountCreated: "اکاؤنٹ کامیابی سے بنایا گیا",
    verified: "تصدیق شدہ",
    verifyEmailPrompt: "تمام خصوصیات تک رسائی کے لیے اپنے ای میل ایڈریس کی تصدیق کریں",
    verifyNow: "ابھی تصدیق کریں",
    currentPassword: "موجودہ پاسورڈ",
    newPassword: "نیا پاسورڈ",
    updateEmail: "ای میل اپڈیٹ کریں",
    updatePassword: "پاسورڈ اپڈیٹ کریں",
    emailUpdated: "ای میل کامیابی سے اپڈیٹ ہو گئی",
    passwordUpdated: "پاسورڈ کامیابی سے اپڈیٹ ہو گیا",
    updateFailed: "اپڈیٹ ناکام ہو گیا۔ براہ کرم دوبارہ کوشش کریں۔",
  },

  alerts: {
    title: "صحت کے انتباہات",
    alertsRequiringAttention: "انتباہات جن کی توجہ کی ضرورت ہے",
    tapToTakeAction: "اقدام کرنے کے لیے انتباہ پر ٹیپ کریں",
    noAlerts: "کوئی انتباہات نہیں",
    allHealthy: "تمام جانور صحت مند ہیں اور صحت کی جانچ اپ ٹو ڈیٹ ہیں",
    loadingAlerts: "انتباہات لوڈ ہو رہے ہیں...",
    healthAbnormalities: "صحت کی غیر معمولی حالات",
    hasHealthAbnormalities: "میں تازہ ترین جانچ میں صحت کی غیر معمولی حالات کا پتہ چلا ہے",
  },

  dashboard: {
    welcome: "خوش آمدید",
    sync: "سنک کریں",
    monitoring: "نگرانی",
    animals: "جانور",
    yourAnimals: "آپ کے جانور",
    sickAnimals: "بیمار جانور",
    healthyAnimals: "صحت مند جانور",
    healthChecks: "صحت کی جانچ",
    stats: {
      totalAnimals: "کل جانور",
      healthyAnimals: "صحت مند جانور",
      healthAlerts: "صحت کے انتباہات",
      checksLast30Days: "چیکس (30 دن)",
    },
    overdueChecks: "تاخیر شدہ صحت کی جانچ",
    isDueForCheck: "صحت کی جانچ کے لیے واجب ہے",
    scheduleCheck: "صحت کی جانچ کریں",
    healthCheckTypes: "صحت کی جانچ کی اقسام",
    recentHealthChecks: "حالیہ صحت کی جانچ",
    recentHealthRecords: "حالیہ صحت کے ریکارڈز",
    viewAll: "سب دیکھیں",
    CaptureorSelectImageforAnalysis: "تجزیہ کے لیے تصویر لیں یا منتخب کریں",
    Takeaphotooftheanimalscondition: "جانور کی حالت کی تصویر لیں (جیسے چڑی، آنکھیں، فضلہ) تجزیہ کے لیے",
  },

  animals: {
    title: "جانور",
    addAnimal: "جانور شامل کریں",
    editAnimal: "جانور میں ترمیم کریں",
    animalDetails: "جانور کی تفصیلات",
    name: "نام",
    namePlaceholder: "جانور کا نام درج کریں",
    nameRequired: "نام درکار ہے",
    species: "نوع",
    speciesPlaceholder: "جیسے گائے، کتا، بکری",
    speciesRequired: "نوع درکار ہے",
    breed: "نسل",
    breedPlaceholder: "جیسے ہولسٹین، جرمن شیفرڈ",
    age: "عمر (سال)",
    agePlaceholder: "جیسے 3",
    ageNumber: "عمر ایک نمبر ہونی چاہیے",
    agePositive: "عمر صفر سے زیادہ ہونی چاہیے",
    weight: "وزن (کلوگرام)",
    weightPlaceholder: "جیسے 450",
    weightNumber: "وزن ایک نمبر ہونا چاہیے",
    gender: "جنس",
    male: "مرد",
    female: "عورت",
    unknown: "نامعلوم",
    birthdate: "تاریخ پیدائش (اختیاری)",
    color: "رنگ",
    markings: "نشانات",
    tagId: "ٹیگ آئی ڈی",
    tagIdPlaceholder: "اختیاری شناختی ٹیگ",
    microchip: "مائیکروچپ",
    notes: "نوٹس",
    lastHealthCheck: "آخری صحت کی جانچ",
    nextHealthCheck: "اگلی صحت کی جانچ",
    healthRecords: "صحت کے ریکارڈز",
    healthChecks: "صحت کی جانچ",
    deleteConfirm: "کیا آپ واقعی اس جانور کو حذف کرنا چاہتے ہیں؟",
    deleteSuccess: "جانور کامیابی سے حذف کر دیا گیا",
    noAnimals: "کوئی جانور نہیں ملا",
    addAnimalsFirst: "دودھ کا ڈیٹا ریکارڈ کرنے سے پہلے براہ کرم اس فارم میں جانور شامل کریں",
    addYourFirstAnimal: "شروع کرنے کے لیے اپنا پہلا جانور شامل کریں",
    addPhoto: "تصویر شامل کریں",
    takePhoto: "تصویر لیں",
    choosePhoto: "لائبریری سے منتخب کریں",
    saveAnimal: "جانور محفوظ کریں",
    addAnimalButton: "جانور شامل کریں",
    addError: "جانور شامل کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔",
    cameraPermission: "تصاویر لینے کے لیے ہمیں کیمرہ کی اجازت درکار ہے",
    cameraRollPermission: "تصاویر اپلوڈ کرنے کے لیے ہمیں کیمرہ رول کی اجازت درکار ہے",
    healthy: "صحت مند",
    unhealthy: "بیمار",
    needAttention: "توجہ کی ضرورت ہے",
    lastVaccine: "آخری ویکسین",
    noVaccineYet: "ابھی تک کوئی ویکسین نہیں",
    noCheckYet: "ابھی تک کوئی چیک نہیں",
    photoAdded: "تصویر شامل کر دی گئی!",
    photoRequired: "جانور کی تصویر درکار ہے",
    ageRequired: "عمر درکار ہے",
    weightRequired: "وزن درکار ہے",
    tagIdRequired: "ٹیگ آئی ڈی درکار ہے",
    loginRequired: "جانور شامل کرنے کے لیے آپ کو لاگ ان ہونا چاہیے",
    addSuccess: "جانور کامیابی سے شامل کر دیا گیا",
    analyzeError: "تصویر کا تجزیہ کرنے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں یا تفصیلات دستی طور پر بھریں۔",
    analyzingImage: "تصویر کا تجزیہ کیا جا رہا ہے...",
    analyzeWithAI: "مصنوعی ذہانت کے ساتھ تجزیہ کریں",
    apiKeyMissing: "اوپن أئی اےپ آئی کی سیٹ نہیں ہے۔ براہ کرم اسے ترتیبات میں شامل کریں۔",
    cow: "گائے",
    goat: "بکری",
    sheep: "بھیڑ",
    pig: "سور",
    poultry: "پولٹری",
    fish: "مچھلی",
    animalWeight: "جانور کا وزن",
    // These are already defined above
    // Keeping the animal-specific translations

    animalBirthdate: "پیدائش کی تاریخ",
    animalTagId: "ٹیگ آئی ڈی",
    tagIdInputPlaceholder: "ٹیگ آئی ڈی درج کریں",
    animalSpecies: "جانور کی قسم",
    breedSelectPlaceholder: "نسل منتخب کریں",
    weightInputPlaceholder: "وزن کلوگرام میں درج کریں",
    breedRequired: "نسل درکار ہے",
    selectAnimalRequired: "براہ کرم جانور منتخب کریں",
    addNewBreed: "نئی نسل شامل کریں",
    enterNewBreed: "نئی نسل درج کریں",
    updateSuccess: "ریکارڈ کامیابی سے اپڈیٹ ہو گیا",
    searchBreeds: "نسلوں کی تلاش کریں...",
    noBreeds: "کوئی نسل نہیں ملی",
    selectCategory: "زمرہ منتخب کریں",

    // Cow breeds
    Sahiwal: "ساہیوال",
    ["Holstein Friesian"]: "ہولسٹین فریزین",
    Jersey: "جرسی",
    ["Red Sindhi"]: "لال سندھی",
    Ayrshire: "ایرشائر",
    ["Brown Swiss"]: "براؤن سوئس",
    Guernsey: "گرنسی",

    // Goat breeds
    Beetal: "بیٹل",
    Boer: "بوئر",
    Jamunapari: "جمناپاری",
    ["Black Bengal"]: "بلیک بنگال",
    Barbari: "بربری",

    // Poultry breeds
    ["Rhode Island Red"]: "روڈ آئلینڈ ریڈ",
    Leghorn: "لیگہورن",
    Broiler: "برائلر",
    Cornish:"کارنِش مرغی",

    // Fish breeds
    Rohu: "روہو",
    Tilapia: "تلاپیا",
    Catla: "کٹلا",

    // More cow breeds
    Gir: "گیر",
    Tharparkar: "تھرپارکر",
    Kankrej: "کانکریج",
    Rathi: "راتھی",
    Hariana: "ہریانا",
    Ongole: "اونگولے",
    Khillari: "کھلاری",
    Deoni: "دیونی",

    // More goat breeds
    Sirohi: "سیروہی",
    Osmanabadi: "عثمان آبادی",
    Malabari: "<|im_start|>�لباری",
    Surti: "سورتی",
    Marwari: "مارواڑی",

    // More poultry breeds
    Australorp: "آسٹریلورپ",
    Orpington: "آرپنگٹن",
    "Plymouth Rock": "پلائماؤتھ راک",
    Sussex: "سسیکس",
    Brahma: "برہما",

    // More fish breeds
    ["Common Carp"]: "عام کارپ",
    ["Grass Carp"]: "گھاس کارپ",
    ["Silver Carp"]: "چاندی کارپ",
    Mrigal: "مریگل",
    Pangasius: "پنگاسیئس",

    // Additional cow breeds
    Nagori: "ناگوری",
    Mewati: "میواتی",
    ["Amrit Mahal"]: "امرت محل",
    ["Kasaragod Dwarf"]: "کاسرگوڈ بونا",
    Vechur: "ویچور",

    // Additional goat breeds
    Mehsana: "مہسانا",
    Kutchi: "کچی",
    Pashmina: "پشمینا",
    Changthangi: "چنگتھانگی",
    ["Kanni Adu"]: "کنی اڈو",
    Zalawadi: "زلاواڈی",
    Tellicherry: "ٹیلیچیری",
    Gohilwadi: "گوہلواڈی",
    Kota: "کوٹا",
    Jhakrana: "جھکرانا",

    // Additional poultry breeds
    Silkie: "سلکی",
    Kadaknath: "کڑکناتھ",
    Aseel: "اصیل",
    Cochin: "کوچن",
    ["Naked Neck"]: "ننگی گردن",
    Fayoumi: "فیومی",
    Ancona: "انکونا",
    Barnevelder: "بارنیولڈر",
    Hamburg: "ہیمبرگ",
    Campine: "کیمپائن",
    Langshan: "لانگشان",
    Java: "جاوا",

    // Additional fish breeds
    Snakehead: "سنیک ہیڈ",
    Koi: "کوئی",
    Guppy: "گپی",
    Goldfish: "گولڈ فش",
    Molly: "مولی",
    Oscar: "آسکر",
    Betta: "بیٹا",
    ["Tinfoil Barb"]: "ٹن فوئل بارب",
    ["Zebra Danio"]: "زیبرا ڈینیو",
    ["Rainbow Shark"]: "رین بو شارک",
    ["Blue Gourami"]: "نیلا گورامی",
    Platy: "پلیٹی",

    // Analysis result translations
    analysisDetected: "پتہ چلا",
    analysisUnknown: "نامعلوم",
    analysisApproximately: "تقریباً",
    analysisYearsOld: "سال کی عمر",
    analysisAgeUnknown: "عمر نامعلوم",
    analysisWithConfidence: "اعتماد کے ساتھ",

    // Animal search and filters
    loading: "جانوروں کو لوڈ کر رہا ہے...",
    searchAnimals: "جانوروں کی تلاش کریں",
    searchSpecific: "{{species}} کی تلاش کریں",
    selectAnimal: "جانور منتخب کریں",
    selectedAnimal: "منتخب شدہ جانور",
    noAnimalsFound: "کوئی جانور نہیں ملا",
    noAnimalsYet: "ابھی تک کوئی جانور نہیں",
    tryDifferentSearch: "مختلف تلاش کی اصطلاح یا فلٹر آزمائیں",
    addFirstAnimal: "اپنے جانوروں کی صحت کی نگرانی شروع کرنے کے لیے اپنا پہلا جانور شامل کریں",

    // Animal detail screen
    loadingDetails: "جانور کی تفصیلات لوڈ ہو رہی ہیں...",
    notFound: "جانور نہیں ملا",
    notFoundMessage: "جس جانور کو آپ تلاش کر رہے ہیں وہ موجود نہیں ہے یا حذف کر دیا گیا ہے۔",
    deleteTitle: "جانور حذف کریں",
    deleteConfirmation: "کیا آپ واقعی اس جانور کو حذف کرنا چاہتے ہیں؟ یہ عمل واپس نہیں کیا جا سکتا۔",
    oneYear: "1 سال",
    multipleYears: "{{age}} سال",
    notScheduled: "شیڈول نہیں",
    nextCheck: "اگلی صحت کی جانچ",
    overdue: "تاخیر شدہ",
    tomorrow: "کل",
    inDays: "{{days}} دنوں میں",
    performCheckNow: "ابھی جانچ کریں",
    scheduleCheck: "جانچ شیڈول کریں",
    type: "جانور کی قسم",

    "father": "پاپ",
    "mother": "ماں ",
    "selectFatherPlaceholder": "پاپ یا مصوبہ منشأ منتخب کریں",
    "selectMotherPlaceholder": "ماں یا مصوبہ منشأ منتخب کریں",
    "boughtFromExternal": "باہر سے خریدا گیا",
    "children": "بچے",
    "noFamilyInfo": "خاندانی معلومات دستیاب نہیں ہیں۔",
    "familyTree": "خاندانی شجرہ",
    

  },

  healthChecks: {
    loading:"صحت کی جانچ لوڈ ہو رہی ہے...",
    performCheck: "صحت کی جانچ کریں",
    title: "صحت کی جانچ",
    addCheck: "صحت کی جانچ شامل کریں",
    editCheck: "صحت کی جانچ میں ترمیم کریں",
    checkDetails: "صحت کی جانچ کی تفصیلات",
    date: "تاریخ",
    temperature: "درجہ حرارت",
    weight: "وزن",
    appetite: "بھوک",
    hydration: "پانی کی مقدار",
    respiration: "سانس لینا",
    gait: "چال/حرکت",
    fecal: "فضلہ کی حالت",
    coat: "کوٹ اور جلد",
    eyes: "آنکھیں اور ناک",
    ears: "کان",
    notes: "نوٹس",
    abnormalities: "غیر معمولی حالات",
    normal: "معمول کے مطابق",
    abnormal: "غیر معمولی",
    increased: "بڑھا ہوا",
    decreased: "کم ہوا",
    none: "کوئی نہیں",
    dehydrated: "پانی کی کمی",
    overhydrated: "زیادہ پانی",
    labored: "مشکل سے",
    limping: "لنگڑانا",
    stiff: "سخت",
    unableToMove: "حرکت کرنے سے قاصر",
    diarrhea: "اسہال",
    constipated: "قبض",
    bloody: "خونی",
    dull: "مدھم",
    patchy: "دھبے دار",
    irritated: "جلن والا",
    discharge: "اخراج",
    cloudy: "دھندلا",
    red: "سرخ",
    swollen: "سوجا ہوا",
    enterTemperature: "درجہ حرارت درج کریں",
    enterWeight: "وزن درج کریں",
    deleteConfirm: "کیا آپ واقعی اس صحت کی جانچ کو حذف کرنا چاہتے ہیں؟",
    deleteSuccess: "صحت کی جانچ کامیابی سے حذف کر دی گئی",
    noChecks: "کوئی صحت کی جانچ نہیں ملی",
    performRegularChecks: "باقاعدگی سے صحت کی جانچ کریں تاکہ جانوروں کی صحت کی نگرانی کی جا سکے",
    captureImageTitle: "تجزیہ کے لیے تصویر لیں یا منتخب کریں",
    captureImageSubtitle: "اے آئی تجزیہ کے لیے جانور کی حالت (جیسے جلد، آنکھیں، فضلہ) کی تصویر لیں",
    takePhoto: "تصویر لیں",
    selectImage: "تصویر منتخب کریں",
    analyzingImage: "تصویر کا تجزیہ کیا جا رہا ہے...",
    analysisError: "تصویر کا تجزیہ کرنے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں یا فارم کو دستی طور پر بھریں۔",
    retryAnalysis: "تجزیہ دوبارہ کریں",
    filterBySpecies: "نوع کے مطابق فلٹر کریں",
    selectAnimal: "جانور منتخب کریں",
    all: "تمام",
    vitalSigns: "اہم علامات",
    physicalCondition: "جسمانی حالت",
    notRecorded: "درج نہیں",
    nextHealthCheck: "اگلی صحت کی جانچ",
    scheduleNextCheck: "اگلی جانچ شیڈول کریں",
    loadingDetails: "صحت کی جانچ کی تفصیلات لوڈ ہو رہی ہیں...",
    notFound: "صحت کی جانچ نہیں ملی",
    notFoundMessage: "جس صحت کی جانچ کو آپ تلاش کر رہے ہیں وہ موجود نہیں ہے یا حذف کر دی گئی ہے۔",
    initialCheckOverdue: "{{name}} کی کبھی بھی صحت کی جانچ نہیں ہوئی۔ ابتدائی صحت کی جانچ واجب الادا ہے۔",
    checkOverdueByDays: "{{name}} کی صحت کی جانچ {{days}} دن سے واجب الادا ہے۔",
    abnormalitiesDetected: "غیر معمولی حالات کا پتہ چلا",
    abnormalitiesWarning: "منتخب اقدار کی بنیاد پر غیر معمولی حالات کا پتہ چلا۔",
    nextCheckDate: "اگلی جانچ کی تاریخ",
    saveHealthCheck: "صحت کی جانچ محفوظ کریں",
    types: {
      temperature: "درجہ حرارت",
      appetite: "بھوک",
      hydration: "پانی کی مقدار",
      breathing: "سانس لینا",
      gait: "حرکت",
      fecal: "فضلہ",
      coat: "کوٹ اور جلد",
      eyes: "آنکھیں اور ناک",
      ears: "کان",
      weight: "وزن",
    },
  },

  records: {
    loading:"ریکارڈز لوڈ ہو رہے ہیں...",
    title: "صحت کے ریکارڈز",
    addRecord: "ریکارڈ شامل کریں",
    editRecord: "ریکارڈ میں ترمیم کریں",
    updateRecord: "ریکارڈ اپڈیٹ کریں",
    updatingRecord: "ریکارڈ کو اپ ڈیٹ کیا جا رہا ہے...",
    saveRecord: "ریکارڈ محفوظ کریں",
    recordDetails: "ریکارڈ کی تفصیلات",
    recordType: "ریکارڈ کی قسم",
    selectRecordType: "ریکارڈ کی قسم منتخب کریں",
    date: "تاریخ",
    symptoms: "علامات",
    diagnosis: "تشخیص",
    treatment: "علاج",
    medicationLabel: "دوا",
    dosage: "خوراک",
    duration: "مدت",
    notes: "نوٹس",
    addNotes: "کوئی اضافی نوٹس یہاں شامل کریں",
    followUp: "فالو اپ",
    cost: "لاگت",
    attachments: "منسلکات",
    veterinarian: "ویٹرنری",
    clinic: "کلینک",
    deleteConfirm: "کیا آپ واقعی اس ریکارڈ کو حذف کرنا چاہتے ہیں؟",
    deleteSuccess: "ریکارڈ کامیابی سے حذف کر دیا گیا",
    noRecords: "کوئی صحت کے ریکارڈز نہیں ملے",
    addRecordsMessage: "طبی تاریخ کی نگرانی کے لیے صحت کے ریکارڈز شامل کریں",
    selectSymptoms: "علامات منتخب کریں",
    confirmedDisease: "تصدیق شدہ بیماری",
    suspectedDiseases: "مشتبہ بیماریاں",
    filterRecords: "ریکارڈز فلٹر کریں",
    allRecords: "تمام ریکارڈز",
    showAllRecords: "تمام صحت کے ریکارڈز دکھائیں",
    vaccination: "ویکسینیشن",
    vaccinations: "ویکسینیشنز",
    vaccinationsDescription: "بیماریوں سے بچاؤ کے لیے دی گئی ویکسینز",
    medications: "دوائیں",
    medicationsDescription: "بیماریوں کے علاج کے لیے دی گئی دوائیں",
    surgery: "سرجری",
    surgeries: "سرجریز",
    surgeriesDescription: "کی گئی سرجیکل پروسیجرز",
    checkup: "چیک اپ",
    checkups: "چیک اپس",
    checkupsDescription: "باقاعدہ صحت کی معائنے",
    details: "تفصیلات",
    searchRecordTypes: "ریکارڈ کی اقسام تلاش کریں...",
    filterHealthRecords: "صحت کے ریکارڈز فلٹر کریں",
    healthRecord: "صحت کا ریکارڈ",
    births: "پیدائش",
    birthsDescription: "پیدائش کے واقعات اور بچوں کی تفصیلات",
    loadingDetails: "ریکارڈ کی تفصیلات لوڈ ہو رہی ہیں...",
    notFound: "ریکارڈ نہیں ملا",
    notFoundMessage: "جس ریکارڈ کو آپ تلاش کر رہے ہیں وہ موجود نہیں ہے یا حذف کر دیا گیا ہے",
    treatments: "علاج",
    severity: {
      low: "کم",
      medium: "درمیانہ",
      high: "زیادہ",
    },
    zoonotic: "زونوٹک",
    contagious: "متعدی",
    nextVaccinationDue: "اگلی ویکسینیشن کی تاریخ",
    nextCheckupDue: "اگلی چیک اپ کی تاریخ",
    vaccineName: "ویکسین کا نام",
    medicineName: "دوا کا نام",
    surgeryType: "سرجری کی قسم",
    birthType: "پیدائش کی قسم",
    checkupType: "چیک اپ کی قسم",
    selectVaccinationDetails: "ویکسین منتخب کریں",
    selectMedicationDetails: "دوا منتخب کریں",
    selectSurgeryDetails: "سرجری منتخب کریں",
    selectBirthDetails: "پیدائش کی قسم منتخب کریں",
    selectGeneralDetails: "چیک اپ کی قسم منتخب کریں",
    searchVaccinationDetails: "ویکسین تلاش کریں...",
    searchMedicationDetails: "دوا تلاش کریں...",
    searchSurgeryDetails: "سرجری تلاش کریں...",
    searchBirthDetails: "پیدائش کی قسم تلاش کریں...",
    searchGeneralDetails: "چیک اپ کی قسم تلاش کریں...",
    selectSurergyDetails: "سرجری کی قسم منتخب کریں",
    selectCheckupDetails: "چیک اپ کی قسم منتخب کریں",
    searchSurgeries: "سرجری تلاش کریں...",
    searchVaccinations: "ویکسین تلاش کریں...",
    searchMedications: "دوائیں تلاش کریں...",
    searchCheckups: "چیک اپ تلاش کریں...",
    searchBirths: "پیدائش تلاش کریں...",
    "detailsPrefix": "تفصیلات:", // Correct
    "practitionerPrefix": "پریکٹیشنر:", // Correct
    "nextVaccinationDuePrefix": "اگلی ویکسینیشن کی تاریخ:", // Correct
    "nextCheckupDuePrefix": "اگلے چیک اپ کی تاریخ:", // Correct


    // Vaccine names and descriptions
    'cow-vac-fmd': "ایف ایم ڈی ویکسین",
    'cow-vac-fmdDescription': "منہ اور کھر کی بیماری سے بچاؤ (ہر 6 ماہ بعد)۔",
    'cow-vac-hs': "ایچ ایس ویکسین",
    'cow-vac-hsDescription': "ہیمورجک سیپٹیسیمیا سے بچاؤ (سالانہ)۔",
    'cow-vac-bq': "بی کیو ویکسین",
    'cow-vac-bqDescription': "بلیک کوارٹر سے بچاؤ (سالانہ)۔",
    'cow-vac-brucellosis': "بروسیلوسس ویکسین",
    'cow-vac-brucellosisDescription': "مادہ بچھڑوں کے لیے بانجھ پن سے بچاؤ۔",

    'goat-vac-ppr': "پی پی آر ویکسین",
    'goat-vac-pprDescription': "پیسٹ ڈیس پیٹیٹس رومینینٹس سے بچاؤ۔",
    'goat-vac-enterotoxemia': "اینٹروٹوکسیمیا ویکسین",
    'goat-vac-enterotoxemiaDescription': "زیادہ کھانے کی بیماری سے بچاؤ۔",
    'goat-vac-fmd-pox': "ایف ایم ڈی / بکری چیچک ویکسین",
    'goat-vac-fmd-poxDescription': "بیماری کے پھیلاؤ سے بچاؤ۔",

    'fish-vac-bacterial': "بیکٹیریل بیماری ویکسین",
    'fish-vac-bacterialDescription': "ایرومونس، وبریو کے لیے (زیادہ تر تجارتی مچھلیوں میں)۔",
    'fish-vac-ipn': "آئی پی این ویکسین",
    'fish-vac-ipnDescription': "انفیکشس پینکریاٹک نیکروسس ویکسین۔",

    'poultry-vac-newcastle': "نیوکاسل بیماری",
    'poultry-vac-newcastleDescription': "سب سے اہم ویکسینوں میں سے ایک۔",
    'poultry-vac-gumboro': "گمبورو (آئی بی ڈی) ویکسین",
    'poultry-vac-gumboroDescription': "2-3 ہفتوں کے چوزوں کے لیے۔",
    'poultry-vac-mareks': "مارکس بیماری",
    'poultry-vac-mareksDescription': "چوزوں کو فور/< بعد پیدائش دی جاتی ہے۔",
    'poultry-vac-ibd': "آئی بی ڈی ویکسین",
    'poultry-vac-ibdDescription': "گومبوڑی کے بجائے۔",
    'poultry-vac-lymphochoriomeningitis': "لامفوکوریومنینگٹسٹس ویکسین",
    'poultry-vac-lymphochoriomeningitisDescription': "ہیڈ ڈریم ٹیکا۔",
    'poultry-vac-coccidiosis': "کوکسیڈیویسیا ویکسین",
    'poultry-vac-coccidiosisDescription': "کوکسیڈیویسیا کے لیے۔",


    // Surgery Name and Description
    "cow-sur-csection": "سی سیکشن",
    "cow-sur-csectionDescription": "مشکل بچہ جنم کی صورت میں آپریشن۔",
    "cow-sur-dehorning": "سنگ اتارنے کا عمل",
    "cow-sur-dehorningDescription": "نوجوان بچھڑوں میں حفاظت کے لیے عام طریقہ۔",
    "cow-sur-abscess": "پھوڑا نکالنا",
    "cow-sur-abscessDescription": "پیپ بھرے سوجن کے لیے معمولی سرجری۔",

    "goat-sur-castration": "خصی کرنا",
    "goat-sur-castrationDescription": "ریوڑ کے نظم و نسق کے لیے عام عمل۔",
    "goat-sur-abscess": "پھوڑے کا علاج",
    "goat-sur-abscessDescription": "انفیکشن والے گٹھلیوں کو نکالنے کی معمولی سرجری۔",


    "fish-sur-rare": "نادر پروسیجرز",
    "fish-sur-rareDescription": "عام طور پر مچھلی کی کاشت میں یہ عمل نہیں کیا جاتا۔",

    'general-surgery': "جنرل پروسیجرز",
    'general-surgeryDescription': "جنرل پروسیجرز کے لیے استعمال ہوتا ہے۔",
    'general-checkup': "چیک اپ",
    'general-checkupDescription': "جنرل چیک اپ کے لیے استعمال ہوتا ہے۔",

    "poultry-sur-uncommon": "نادر پروسیجرز",
    "poultry-sur-uncommonDescription": "نادر پروسیجرز کے لیے استعمال ہوتا ہے۔",

    // Medicine Name and Description
    "cow-med-antibiotics": "اینٹی بایوٹک دوا",
    "cow-med-antibioticsDescription": "بیکٹیریا کے انفیکشن جیسے ماسٹائٹس، نمونیا کے علاج کے لیے۔",
    "cow-med-anthelmintics": "پیٹ کے کیڑے مار دوا (اینتهلمینٹک)",
    "cow-med-anthelminticsDescription": "پیٹ کے کیڑوں جیسے آنتوں کے کیڑوں کے خاتمے کے لیے۔",
    "cow-med-pain-relievers": "درد کم کرنے والی دوا",
    "cow-med-pain-relieversDescription": "سوجن یا چوٹ کی وجہ سے ہونے والے درد کے لیے NSAIDs۔",
    "cow-med-digestive": "ہاضمہ بڑھانے والی دوا",
    "cow-med-digestiveDescription": "رومین کے نظام اور بھوک کو بہتر بنانے کے لیے۔",



    "goat-med-coccidiostats": "کوکسیڈیوسٹیٹس",
    "goat-med-coccidiostatsDescription": "کوکسیڈیوسس کی روک تھام/علاج کے لیے۔",
    "goat-med-antibiotics": "اینٹی بائیوٹکس",
    "goat-med-antibioticsDescription": "نمونیا، انفیکشن کے لیے۔",
    "goat-med-dewormers": "کیڑے مار دوا",
    "goat-med-dewormersDescription": "علاقے کے مطابق ہر 3-6 ماہ بعد۔",


    "poultry-med-antibiotics": "اینٹی بایوٹکس",
    "poultry-med-antibioticsDescription": "سانس کی انفیکشن اور بیکٹیریل بیماریوں کے لیے استعمال ہوتا ہے۔",
    "poultry-med-coccidiostats": "کوکسیڈیوسٹیٹس",
    "poultry-med-coccidiostatsDescription": "کوکسیڈیوسس کی روک تھام اور علاج کے لیے استعمال ہوتا ہے۔",
    "poultry-med-vitamins": "وٹامن سپلیمنٹس",
    "poultry-med-vitaminsDescription": "نشوونما اور مدافعتی نظام کی حمایت کے لیے استعمال ہوتا ہے۔",
    "poultry-med-probiotics": "پروبایوٹکس",
    "poultry-med-probioticsDescription": "معدے کی صحت اور بہتر ہضم کے لیے استعمال ہوتا ہے۔",

    "fish-med-antibacterials": "بیکٹیریا مار دوا",
    "fish-med-antibacterialsDescription": "پانی سے پھیلنے والی بیکٹیریل انفیکشن کے لیے۔",
    "fish-med-antifungals": "فنگس مار دوا",
    "fish-med-antifungalsDescription": "فنگل پھیلاؤ میں استعمال (سفید داغ)۔",
    "fish-med-water": "پانی کے کنڈیشنرز",
    "fish-med-waterDescription": "ٹینک/تالاب کی صحت بہتر بنائیں۔",

    //Checkup Name and Description
    "cow-check-weekly": "ہفتہ وار صحت معائنہ",
    "cow-check-weeklyDescription": "عمومی معائنہ: آنکھیں، بالوں کی حالت، بخار وغیرہ۔",
    "cow-check-hoof": "کھُر کا معائنہ",
    "cow-check-hoofDescription": "انفیکشن یا زیادہ بڑھنے کی جانچ کریں۔",
    "cow-check-feeding": "خوراک کا رویہ",
    "cow-check-feedingDescription": "بھوک اور پانی پینے کی عادت کا مشاہدہ کریں۔",

    "goat-check-weekly": "ہفتہ میں بارے دفعہ چیک اپ",
    "goat-check-weeklyDescription": "ہفتہ میں بارے دفعہ چیک اپ کے لیے استعمال ہوتا ہے۔",
    "goat-check-hoof": "ہواف کی جانچ",
    "goat-check-hoofDescription": "ہواف کی جانچ کے لیے استعمال ہوتا ہے۔",
    "goat-check-feeding": "�انے کی جانچ",
    "goat-check-feedingDescription": "�انے کی جانچ کے لیے استعمال ہوتا ہے۔",
    "goat-check-parasite": "پیریٹس کی جانچ",
    "goat-check-parasiteDescription": "پیریٹس کی جانچ کے لیے استعمال ہوتا ہے۔",


    "poultry-check-feather": "پَر چیک کرنا",
    "poultry-check-featherDescription": "پروں کا جھڑنا = تناؤ یا جوؤں کی علامت۔",
    "poultry-check-leg": "ٹانگوں کی طاقت کا معائنہ",
    "poultry-check-legDescription": "خاص طور پر برائلر مرغیوں میں۔",


    "fish-check-water": "پانی کے معیار کی جانچ",
    "fish-check-waterDescription": "پی ایچ، آکسیجن، امونیا کی سطحیں۔",
    "fish-check-behavior": "سلوک کی جانچ",
    "fish-check-behaviorDescription": " بے قاعدہ تیرنا، بھوک میں کمی۔",

    "general-check-weekly": "ہفتہ میں بارے دفعہ چیک اپ",
    "general-check-weeklyDescription": "ہفتہ میں بارے دفعہ چیک اپ کے لیے استعمال ہوتا ہے۔",
    "general-check-monthly": "ہر ماہ میں چیک اپ",
    "general-check-monthlyDescription": "ہر ماہ میں چیک اپ کے لیے استعمال ہوتا ہے۔",
    "general-check-quarterly": "ہر چار ماہ میں چیک اپ",
    "general-check-quarterlyDescription": "ہر چار ماہ میں چیک اپ کے لیے استعمال ہوتا ہے۔",
    "general-check-biannually": "ہر دو سال میں چیک اپ",
    "general-check-biannuallyDescription": "ہر دو سال میں چیک اپ کے لیے استعمال ہوتا ہے۔",




    //BIRTH Name and Description
    "cow-birth-normal": "عام طور پر بچھڑے کی پیدائش",
    "cow-birth-normalDescription": "عام، قدرتی پیدائش کا ریکارڈ۔",
    "cow-birth-assisted": "مدد سے بچھڑے کی پیدائش",
    "cow-birth-assistedDescription": "دستی یا ڈاکٹر کی مدد سے ڈیلیوری کی ضرورت۔",
    "cow-birth-stillbirth": "مردہ بچھڑا",
    "cow-birth-stillbirthDescription": "ناکام پیدائش کا ریکارڈ۔",

    "goat-birth-normal": "عام طور پر بچہ ہونا",
    "goat-birth-normalDescription": "عام، قدرتی پیدائش کا ریکارڈ۔",
    "goat-birth-assisted": "مدد سے بچہ ہونا",
    "goat-birth-assistedDescription": "دستی یا ڈاکٹر کی مدد سے ڈیلیوری کی ضرورت۔",
    "goat-birth-stillbirth": "مردہ بچہ",
    "goat-birth-stillbirthDescription": "ناکام پیدائش کا ریکارڈ۔",

    "poultry-birth-egg": "انڈے کی پیداوار",
    "poultry-birth-eggDescription": "انڈے کی پیداوار کو فی مرغی/بیچ کے لحاظ سے ٹریک کریں۔",
    "poultry-birth-hatch": "ہیٹچ رپورٹ",
    "poultry-birth-hatchDescription": "انکیوبیٹر یا قدرتی ہچنگ سے چوزوں کی تعداد",

    "fish-birth-hatch": "بچے نکلنے کی شرح",
    "fish-birth-hatchDescription": "لاگ کریں کہ کتنے چھوٹے مچھلی کے بچے زندہ رہتے ہیں۔",
    "fish-birth-spawning": "انڈے دینے کا ریکارڈ",
    "fish-birth-spawningDescription": "نوٹ کریں کہ مچھلی کب انڈے دیتی ہے۔",


    "practitionerPlaceholder": "پرکٹیسٹر کا نام درج کریں",
    practitioner: "پرکٹیسٹر",
    clinicPlaceholder: "کلینیک کا نام درج کریں",
    addNew: "نیا {{type}} شامل کریں",
    vaccine: "ویکسین",
    medicine: "دوا",
    recording: "ریکارڈنگ...",
    recordingStarted: "ویس ریکارڈنگ شروع ہو گیا",
    recordingStopped: "ویس ریکارڈنگ بند ہو گیا",
    recordingFailed: "ریکارڈنگ شروع کرنے میں ناکامی",
    self: "خود",
    selfTreatment: "خود علاج",


    // name for card
    "antibiotics": "اینٹی بایوٹکس",
    "anthelmintics(dewormers)": "اینٹی ہیلمنٹکس (ڈیورمرز)",
    "painrelievers": "درد کم کرنے والے",
    "digestiveboosters": "ہاضمے کو بڑھانے والے",
    "fmdvaccine": "ایف ایم ڈی ویکسین",
    "hsvaccine": "ایچ ایس ویکسین",
    "bqvaccine": "بی کیو ویکسین",
    "brucellosisvaccine": "برسیلوسیس ویکسین",
    "csection": "سی سیکشن",
    "hornremoval(dehorning)": "سینگ ہٹانا (ڈی ہارنگ)",
    "abscessdrainage": "پھوڑے کا نکاسی",
    "normalcalving": "عام زچگی",
    "assistedcalving": "مدد کی زچگی",
    "stillbirth": "مردہ پیدا ہونا",
    "weeklyhealthcheck": "ہفتہ وار صحت کی جانچ",
    "hoofinspection": "کھروں کی جانچ",
    "feedingbehavior": "خوراک کا رویہ",
    "coccidiostats": "کوکسیڈیوسٹٹس",
    "dewormers": "ڈیورمرز",
    "enterotoxemiavaccine": "انٹرٹوکسیما ویکسین",
    "fmd/goatpoxvaccines": "ایف ایم ڈی / بکری پکس ویکسین",
    "castration": "خصی کرنا",
    "abscesstreatment": "پھوڑے کا علاج",
    "spawningrecord": "انڈے دینے کا ریکارڈ",
    "hatchrate": "ہچھی کی شرح",
    "waterqualitycheck": "پانی کے معیار کی جانچ",
    "behaviorcheck": "سلوک کی جانچ",
    "vitaminsupplements": "وٹامن سپلیمنٹس",
    "newcastledisease": "نیوکاسل بیماری",
    "gumboro(ibd)vaccine": "گمبورو (آئی بی ڈی) ویکسین",
    "mareksdisease": "میریک کی بیماری",
    "featherhealthcheck": "پر کی صحت کی جانچ",
    "legstrengthcheck": "ٹانگ کی طاقت کی جانچ"
  },
  settings: {
    title: "ترتیبات",
    account: "اکاؤنٹ",
    profile: "پروفائل",
    language: "زبان",
    languages: {
      en: "انگریزی",
      ur: "اردو",
      pa: "پنجابی",
    },
    darkMode: "ڈارک موڈ",
    darkModeDescription: "رات کے وقت دیکھنے کے لیے ڈارک تھیم استعمال کریں",
    notifications: "نوٹیفیکیشنز",
    notificationsEnabled: "نوٹیفیکیشنز کامیابی سے فعال ہو گئیں",
    syncSettings: "سنک ترتیبات",
    offlineMode: "آف لائن موڈ",
    autoSync: "آٹو سنک",
    dataUsage: "ڈیٹا استعمال",
    accessibility: "رسائی",
    audioFeedback: "آڈیو فیڈبیک",
    preferences: "ترجیحات",
    sound: "آواز",
    soundDescription: "ایپ میں آواز کے اثرات کو فعال کریں",
    textSize: "متن کا سائز",
    textSizeSmall: "چھوٹا",
    textSizeMedium: "درمیانہ",
    textSizeLarge: "بڑا",
    about: "ہمارے بارے میں",
    help: "مدد اور سپورٹ",
    contactUs: "ہم سے رابطہ کریں",
    termsOfService: "سروس کی شرائط",
    privacyPolicy: "رازداری کی پالیسی",
    logout: "لاگ آؤٹ",
    logoutTitle: "لاگ آؤٹ",
    logoutMessage: "کیا آپ واقعی لاگ آؤٹ کرنا چاہتے ہیں؟",
    logoutConfirm: "کیا آپ واقعی لاگ آؤٹ کرنا چاہتے ہیں؟",
    logoutErrorTitle: "لاگ آؤٹ میں خرابی",
    logoutErrorMessage: "لاگ آؤٹ کرنے میں مسئلہ تھا۔ براہ کرم دوبارہ کوشش کریں۔",
    version: "ورژن",
    emailPassword: "ای میل اور پاسورڈ",
    privacySecurity: "رازداری اور سیکیورٹی",
    support: "سپورٹ",
    aboutApp: "ایپ کے بارے میں",
    aboutAppDescription: "اس ایپلیکیشن کے بارے میں مزید جانیں",
    aiFeatures: "مصنوعی ذہانت کی خصوصیات",
    aiVisionAnalysis: "مصنوعی ذہانت ویژن تجزیہ",
    aiVisionDescription: "تصاویر سے جانوروں کی نوع، نسل، اور جنس کا خودکار پتہ لگائیں",
    accountSettings: "اکاؤنٹ کی ترتیبات",
    accountSettingsDescription: "اپنی اکاؤنٹ کی معلومات کا انتظام کریں",
    profileDescription: "اپنی پروفائل کی معلومات دیکھیں",
    appearance: "ظاہری شکل",
    healthTools: 'صحت کے آلات',
    symptomsChecker: 'علامات چیکر',
    symptomsCheckerDescription: 'جانوروں کی علامات اور ممکنہ حالات چیک کریں',
  },

  tabs: {
    dashboard: "ڈیش بورڈ",
    animals: "جانور",
    farms: "فارمز",
    expenses: "اخراجات",
    settings: "ترتیبات",
    tasks: "تسکز",
    pregnancy: "حمل",

  },
  tasks: {
    loading:"کام لوڈ ہو رہے ہیں...",
    "description": "تفصیل",
    "descriptionPlaceholder": "ٹاسک کی تفصیل درج کریں یا ریکارڈ کرنے کے لیے مائیکروفون پر ٹیپ کریں",
    "notes": "نوٹس",
    "notesPlaceholder": "اضافی نوٹس درج کریں یا ریکارڈ کرنے کے لیے مائیکروفون پر ٹیپ کریں",
    "status": {
      "completed": "مکمل" // Or your Urdu translation for Completed
    },
    "priorities": {
      "high": "اعلی",    // Or your Urdu translation for High
      "medium": "درمیانہ", // Or your Urdu translation for Medium
      "low": "کم"       // Or your Urdu translation for Low
    },
    updateSuccess: "کام کامیابی سے اپ ڈیٹ ہو گیا",
updateError: "کام کو اپ ڈیٹ کرنے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں",
deleteSuccess: "کام کامیابی سے حذف کر دیا گیا",
deleteError: "کام کو حذف کرنے میں ناکامی۔ براہ کرم دوبارہ کوشش کریں",
editTask:"کام میں ترمیم کریں",
updateTask: "کام کو اپ ڈیٹ کریں",
    "title": "کام",
    "tasks": "کام",
    "all": "تمام",
    "pending": "زیر التوا",
    "completed": "مکمل",
    "noTasks": "کوئی کام نہیں ملا",
    "noTasksMessage": "آپ کے پاس ابھی کوئی کام نہیں ہے۔",
    "addTask": "کام شامل کریں",
    "taskDetails": "کام کی تفصیلات",
    "markComplete": "مکمل کے طور پر نشان زد کریں",
    "markCompleteConfirm": "کیا آپ واقعی اس کام کو مکمل کے طور پر نشان زد کرنا چاہتے ہیں؟",
    "reopenTask": "کام دوبارہ کھولیں",
    "reopenTaskConfirm": "کیا آپ واقعی اس کام کو دوبارہ کھولنا چاہتے ہیں؟",
    "deleteTask": "کام حذف کریں",
    "deleteTaskConfirm": "کیا آپ واقعی اس کام کو حذف کرنا چاہتے ہیں؟ یہ کارروائی ناقابل واپسی ہے۔",
    "notFound": "کام نہیں ملا",
    "details": "تفصیلات",
    "assignedTo": "تفویض کردہ",
    "dueDate": "آخری تاریخ",
    "priority": "ترجیح",
    "markAsComplete": "مکمل کے طور پر نشان زد کریں",
    "errors": {
      "titleRequired": "عنوان درکار ہے۔",
      "farmRequired": "فارم درکار ہے۔",
      "assigneeRequired": "تفویض کردہ شخص درکار ہے۔"
    },
    "titlePlaceholder": "کام کا عنوان درج کریں",
    // "descriptionPlaceholder": "کام کی تفصیل درج کریں (اختیاری)",
    "selectAssignee": "تفویض کردہ شخص منتخب کریں",
    "selectFarmFirst": "ملازمین کو دیکھنے کے لیے پہلے فارم منتخب کریں۔",
    "noEmployeesInFarm": "اس فارم میں کوئی ملازم نہیں ہے۔",
    "selectPriorityPlaceholder": "ترجیح منتخب کریں",
    "selectPriority": "ترجیح منتخب کریں",
    "selectRecurrencePlaceholder": "تکرار منتخب کریں",
    "selectRecurrence": "تکرار منتخب کریں",
    searchPriorities: 'ترجیحات تلاش کریں',
    searchRecurrences: 'تکرار تلاش کریں',
    // "notesPlaceholder": "نوٹس درج کریں (اختیاری)",
    "addSuccess": "کام کامیابی سے شامل کر دیا گیا ہے۔",
    "priorityValue": {
      "low": "کم",
      "medium": "درمیانہ",
      "high": "اعلی"
    },
    "recurrence": {
      "once": "ایک بار",
      "daily": "روزانہ",
      "weekly": "ہفتہ وار",
      "monthly": "ماہانہ",
      "yearly": "سالانہ"
    },
    "overdue": "مدت ختم",
    assignTo: "تفویض کریں",
    recurrences: "تکرار",
    recurrenceValues: {
      once: "ایک بار",
      daily: "روزانہ",
      weekly: "ہفتہ وار",
      monthly: "ماہانہ"
    },
    farm: "فارم",

  },
  "staff": {
    loading:"عملہ لوڈ ہو رہے ہیں...",
    "staff": "عملہ",
    "assignStaff": "عملہ تفویض کریں",
    "noStaff": "کوئی عملہ نہیں ملا",
    "addStaffMembers": "اپنے فارم میں عملہ کے اراکین شامل کریں۔",
    "noStaffAvailable": "اس فارم میں کوئی عملہ دستیاب نہیں ہے۔",
    "addStaff": "عملہ شامل کریں"
  },
  activities: {
    loading: "سرگرمیاں لوڈ ہو رہی ہیں...",
    title: "حالیہ سرگرمیاں",
    recentActivities: "حالیہ سرگرمیاں",
    noActivities: "کوئی سرگرمی نہیں",
    noActivitiesMessage: "کوئی حالیہ سرگرمی نہیں ملی۔",
    activityDetails: "سرگرمی کی تفصیلات",
    createdBy: "بنانے والا",
    createdAt: "بنانے کی تاریخ",
    updatedBy: "اپڈیٹ کرنے والا",
    updatedAt: "اپڈیٹ کی تاریخ",
    // خودکار طور پر تیار شدہ سرگرمی کی اقسام
    animalAdded: "جانور شامل کیا گیا",
    healthRecordAdded: "صحت کا ریکارڈ شامل کیا گیا",
    vaccinationRecordAdded: "ویکسینیشن ریکارڈ شامل کیا گیا",
    generalRecordAdded: "عمومی ریکارڈ شامل کیا گیا",
    pregnancyAdded: "حمل کا ریکارڈ شامل کیا گیا",
    aiCrossPregnancyAdded: "AI کراس حمل شامل کیا گیا",
    naturalPregnancyAdded: "قدرتی حمل شامل کیا گیا",
    milkingRecordAdded: "دودھ نکالنے کا ریکارڈ شامل کیا گیا"
  },

  account: {
    updateAccount: "اکاؤنٹ اپڈیٹ کریں",
    updateEmail: "ای میل اپڈیٹ کریں",
    updatePassword: "پاسورڈ اپڈیٹ کریں",
    currentEmail: "موجودہ ای میل",
    newEmail: "نئی ای میل",
    currentPassword: "موجودہ پاسورڈ",
    newPassword: "نیا پاسورڈ",
    confirmNewPassword: "نئے پاسورڈ کی تصدیق کریں",
    saveChanges: "تبدیلیاں محفوظ کریں",
    emailUpdated: "ای میل کامیابی سے اپڈیٹ ہو گئی",
    passwordUpdated: "پاسورڈ کامیابی سے اپڈیٹ ہو گیا",
    updateFailed: "اپڈیٹ ناکام ہو گیا۔ براہ کرم دوبارہ کوشش کریں۔",
    passwordsDontMatch: "نئے پاسورڈ میچ نہیں کرتے",
    passwordTooShort: "پاسورڈ کم از کم 6 حروف کا ہونا چاہیے",
    invalidEmail: "براہ کرم درست ای میل ایڈریس درج کریں",
  },

  profile: {
    title: "پروفائل",
    personalInfo: "ذاتی معلومات",
    accountInfo: "اکاؤنٹ کی معلومات",
    statistics: "اعداد و شمار",
    name: "نام",
    email: "ای میل",
    role: "کردار",
    memberSince: "رکن بننے کی تاریخ",
    emailVerified: "ای میل کی تصدیق",
    verified: "تصدیق شدہ",
    notVerified: "غیر تصدیق شدہ",
    farmsOwned: "ملکیتی فارمز",
    farmsAssigned: "تفویض کردہ فارمز",
    totalAnimals: "کل جانور",
    profilePicture: "پروفائل تصویر",
    changeProfilePicture: "پروفائل تصویر تبدیل کریں",
    removeProfilePicture: "پروفائل تصویر ہٹائیں",
    selectImageSource: "تصویر کا ذریعہ منتخب کریں",
    camera: "کیمرہ",
    gallery: "گیلری",
    roles: {
      owner: "مالک",
      admin: "منتظم",
      caretaker: "نگہبان",
    },
  },

  language: {
    selectLanguage: "زبان منتخب کریں",
    english: "انگریزی",
    urdu: "اردو",
    punjabi: "پنجابی",
  },

  about: {
    appName: "لائیوسٹاک ٹریکر",
    version: "ورژن",
    aboutTitle: "ہمارے بارے میں",
    description: "لائیوسٹاک ٹریکر ایک جامع ایپ ہے جو کسانوں اور مویشیوں کے مالکوں کو اپنے جانوروں کو مؤثر طریقے سے منظم کرنے میں مدد کرنے کے لیے ڈیزائن کی گئی ہے۔ صحت کے ریکارڈ رکھیں، چیک اپ شیڈول کریں، اور اپنے مویشیوں کی فلاح و بہبود کی نگرانی ایک ہی جگہ پر کریں۔",
    featuresTitle: "خصوصیات",
    featureHealthTracking: "صحت کی نگرانی اور ٹریکنگ",
    featureSymptomId: "علامات کی شناخت",
    featureOfflineAccess: "اہم ڈیٹا تک آف لائن رسائی",
    featureLanguageSupport: "متعدد زبانوں کی سپورٹ",
    contactTitle: "رابطہ",
    legalTitle: "قانونی",
    copyright: "لائیوسٹاک ٹریکر۔ جملہ حقوق محفوظ ہیں۔",
  },

  farms: {
    loading:"فارمز لوڈ ہو رہے ہیں...",
    title: "فارمز",
    farm: "فارم",
    expenses: "اخراجات",
    selectFarms: 'فارم منتخب کریں',
    selectFarmsPlaceholder: 'فارم منتخب کریں',
    addFarm: "فارم شامل کریں",
    editFarm: "فارم میں ترمیم کریں",
    farmDetails: "فارم کی تفصیلات",
    name: "فارم کا نام",
    namePlaceholder: "فارم کا نام درج کریں",
    nameRequired: "فارم کا نام درکار ہے",
    location: "مقام",
    locationPlaceholder: "مقام درج کریں",
    locationRequired: "مقام درکار ہے",
    status: "حالت",
    statusActive: "فعال",
    statusInactive: "غیر فعال",
    statusCompleted: "مکمل",
    statusPending: "زیر التواء",
    statusActiveDescription: "فارمز جو فی الحال فعال اور استعمال میں ہیں",
    statusInactiveDescription: "فارمز جو اب استعمال میں نہیں ہیں",
    statusCompletedDescription: "فارمز جو مکمل ہو چکے ہیں",
    statusPendingDescription: "فارمز جو منظوری یا کارروائی کا انتظار کر رہے ہیں",
    animals: "جانور",
    staff: "عملہ",
    milking: "دودھ نکالنا",
    noFarms: "کوئی فارم نہیں ملا",
    addYourFirstFarm: "شروع کرنے کے لیے اپنا پہلا فارم شامل کریں",
    saveFarm: "فارم محفوظ کریں",
    addError: "فارم شامل کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔",
    addSuccess: "فارم کامیابی سے شامل کر دیا گیا",
    deleteConfirm: "کیا آپ واقعی اس فارم کو حذف کرنا چاہتے ہیں؟",
    deleteSuccess: "فارم کامیابی سے حذف کر دیا گیا",
    viewAnimals: "جانور دیکھیں",
    addEmployee: "ملازم شامل کریں",
    assignTask: "کام تفویض کریں",
    filterFarms: "فارمز فلٹر کریں",
    filterByStatus: "حالت کے مطابق فلٹر کریں",
    searchFarms: "فارمز تلاش کریں",
    farmName: "فارم کا نام",
    farmNamePlaceholder: "فارم منتخب کریں",
    farmNameRequired: "فارم کا نام درکار ہے",
    requiredTitle: "فارم درکار ہے",
    requiredMessage: "دیگر خصوصیات تک رسائی حاصل کرنے سے پہلے آپ کو ایک فارم بنانے کی ضرورت ہے۔ براہ کرم پہلے ایک فارم شامل کریں۔",
    updateSuccess: "فارم کامیابی سے اپڈیٹ ہو گیا",
    updateError: "فارم اپڈیٹ کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔",
    noStaff: "کوئی عملہ نہیں ملا",
    addYourFirstStaff: "شروع کرنے کے لیے اپنا پہلا عملہ شامل کریں",
    expenseAnalytics: "تجزیات",
    selectStatus: "حالت منتخب کریں",
    selectFarm: "فارم منتخب کریں",
    selectFarmPlaceholder: "فارم منتخب کریں",
    allFarms: "تمام فارمز",
    showAllFarms: "تمام فارمز کے اعدادوشمار دکھائیں",
    address: "پتہ",
    animalCount: "جانوروں کی تعداد",
    staffCount: "عملے کی تعداد",
    totalExpenses: "کل اخراجات",
    tasks: {
      title: "کام",
      myTasks: "میرے کام",
      tasks: "کام",
      manage: "انتظام کریں",
      addTask: "کام شامل کریں",
      taskDetails: "کام کی تفصیلات",
      all: "تمام",
      pending: "زیر التواء",
      completed: "مکمل",
      overdue: "تاخیر شدہ",
      inProgress: "جاری",
      noTasks: "کوئی کام نہیں",
      noTasksMessage: "آپ کو ابھی تک کوئی کام تفویض نہیں کیا گیا ہے۔",
      titlePlaceholder: "کام کا عنوان درج کریں",
      description: "تفصیل",
      descriptionPlaceholder: "کام کی تفصیل درج کریں",
      dueDate: "مقررہ تاریخ",
      priority: "ترجیح",
      priorityLevels: {
        low: "کم",
        medium: "درمیانی",
        high: "اعلی"
      },
      selectPriority: "ترجیح منتخب کریں",
      selectPriorityPlaceholder: "ترجیح کی سطح منتخب کریں",
      assignTo: "تفویض کریں",
      assignedTo: "تفویض کردہ",
      selectAssignee: "تفویض کنندہ منتخب کریں",
      selectAssigneePlaceholder: "عملے کا رکن منتخب کریں",
      searchEmployees: "ملازمین تلاش کریں",
      notes: "نوٹس",
      notesPlaceholder: "کوئی اضافی نوٹس شامل کریں",
      details: "کام کی تفصیلات",
      markAsComplete: "مکمل کے طور پر نشان زد کریں",
      markComplete: "کام مکمل کریں",
      markCompleteConfirm: "کیا آپ واقعی اس کام کو مکمل کے طور پر نشان زد کرنا چاہتے ہیں؟",
      deleteTask: "کام حذف کریں",
      deleteConfirm: "کیا آپ واقعی اس کام کو حذف کرنا چاہتے ہیں؟ یہ عمل واپس نہیں کیا جا سکتا۔",
      deleted: "کام کامیابی سے حذف کر دیا گیا",
      markedComplete: "کام مکمل کے طور پر نشان زد کر دیا گیا",
      addSuccess: "کام کامیابی سے شامل کر دیا گیا",
      notFound: "کام نہیں ملا",

      selectFarmFirst: "پہلے فارم منتخب کریں",
      noEmployeesInFarm: "اس فارم میں کوئی ملازم نہیں ملا۔ پہلے فارم میں ملازمین شامل کریں۔",
      recurrences: "تکرار",
      selectRecurrence: "تکرار منتخب کریں",
      selectRecurrencePlaceholder: "یہ کام کتنی بار دہرایا جائے؟",
      recurrenceValues: {
        once: "ایک بار",
        daily: "روزانہ",
        weekly: "ہفتہ وار",
        monthly: "ماہانہ"
      },
      errors: {
        titleRequired: "کام کا عنوان ضروری ہے",
        farmRequired: "براہ کرم فارم منتخب کریں",
        assigneeRequired: "براہ کرم تفویض کنندہ منتخب کریں"
      },
      
    },
    staffSection: {
      title: "عملہ",
      addStaff: "عملے کا رکن شامل کریں",
      editStaff: "عملے کے رکن میں ترمیم کریں",
      staffDetails: "عملے کی تفصیلات",
      name: "نام",
      namePlaceholder: "عملے کا نام درج کریں",
      nameRequired: "عملے کا نام درکار ہے",
      role: "کردار",
      rolePlaceholder: "عملے کا کردار درج کریں",
      roleRequired: "عملے کا کردار درکار ہے",
      contact: "رابطہ",
      contactPlaceholder: "رابطے کی معلومات درج کریں",
      contactRequired: "رابطے کی معلومات درکار ہے",
      saveStaff: "عملے کا رکن محفوظ کریں",
      addError: "عملے کا رکن شامل کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔",
      addSuccess: "عملے کا رکن کامیابی سے شامل کر دیا گیا",
      deleteConfirm: "کیا آپ واقعی اس عملے کے رکن کو حذف کرنا چاہتے ہیں؟",
      deleteSuccess: "عملے کا رکن کامیابی سے حذف کر دیا گیا",
      noStaff: "کوئی عملے کا رکن نہیں ملا",
      addYourFirstStaff: "شروع کرنے کے لیے اپنا پہلا عملے کا رکن شامل کریں",
      addEmployee: "ملازم شامل کریں",
      cnic: 'قومی شناختی کارڈ',
      cnicPlaceholder: "CNIC درج کریں",
      cnicRequired: "CNIC درکار ہے",
      joiningDate: "شمولیت کی تاریخ",
      joiningDatePlaceholder: "شمولیت کی تاریخ درج کریں",
      gender: "جنس",
      genderPlaceholder: "جنس درج کریں",
      genderRequired: "جنس درکار ہے",
      status: "حالت",
      statusActive: "فعال",
      statusInactive: "غیر فعال",
      statusCompleted: "مکمل",
      statusPending: "زیر التواء",
      roles: {
        admin: "منتظم",
        caretaker: "نگہبان",
        owner: "مالک"
      },
      employeeDetails: "ملازم کی تفصیلات",
      addPhoto: 'تصویر شامل کریں',
      takePhoto: 'تصویر گرفت کریں',
      choosePhoto: 'کتابخانہ سے انتخاب کریں',
      saveEmployee: 'ملازم محفوظ کریں',
      updateSuccess: 'ملازم کامیابی سے اپڈیٹ ہو گیا',
      updateError: 'ملازم اپڈیٹ کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔',
      deleteEmployee: 'ملازم حذف کریں',

    },
    accessibleFarms: "وصول پذیر فارمز",
    noAssignedFarms: "اس ملازم کو کوئی وصول پذیر فارم نہیں ہے",
    expensesByCategory: "زمرہ کے لحاظ سے اخراجات",
    expenseDetails: "اخراجات کی تفصیلات",
      selectType: "قسم منتخب کریں",
      type: "قسم",
      size: "سائز",
      sizePlaceholder: "0.0",
      sizeUnit: "سائز کی اکائی",
      selectUnit: "اکائی منتخب کریں",
      descriptionPlaceholder: "کھیت کی تفصیل درج کریں"
  },
  expenses: {
    fixedCurrency: "تمام اخراجات کے لیے کرنسی پاکستانی روپیہ (PKR) پر مقرر ہے",
    title: "اخراجات",
    noExpenses: "کوئی اخراجات نہیں",
    addYourFirstExpense: "لاگت کو ٹریک کرنے کے لیے اپنا پہلا خرچہ شامل کریں",
    addExpense: "خرچہ شامل کریں",
    addNew: "نیا خرچہ شامل کریں",
    editExpense: "خرچہ میں ترمیم کریں",
    expenseDetails: "خرچے کی تفصیلات",
    amount: "رقم",
    totalExpenses: 'کل اخراجات',
    amountPlaceholder: "رقم درج کریں",
    date: "تاریخ",
    category: {
      label: "زمرہ",
      select: "زمرہ منتخب کریں",
      animalPurchase: "جانور کی خریداری",
      feed: "خوراک",
      medication: "دوا",
      vaccination: "ویکسینیشن",
      veterinary: "ویٹرنری خدمات",
      equipment: "آلات",
      utilities: "یوٹیلیٹیز",
      labor: "مزدوری",
      maintenance: "دیکھ بھال",
      other: "دیگر"
    },
    paymentMethod: {
      label: "ادائیگی کا طریقہ",
      select: "ادائیگی کا طریقہ منتخب کریں",
      cash: "نقد",
      card: "کارڈ",
      bankTransfer: "بینک ٹرانسفر",
      other: "دیگر"
    },
    receipt: "رسید",
    descriptionPlaceholder: "اس خرچے کے بارے میں تفصیل یا نوٹس درج کریں",
    filterByFarm: "فارم کے مطابق فلٹر کریں",
    validation: {
      amount: "براہ کرم درست رقم درج کریں",
      category: "براہ کرم زمرہ منتخب کریں",
      farm: "براہ کرم فارم منتخب کریں",
      paymentMethod: "براہ کرم ادائیگی کا طریقہ منتخب کریں"
    },
    addSuccess: "خرچہ شامل کیا گیا",
    addSuccessDetail: "خرچہ کامیابی سے شامل کیا گیا ہے",
    addError: "خرچہ شامل کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔",
    pickImage: "تصویر منتخب کریں",
    addReceipt: "رسید شامل کریں",
    receiptAdded: "رسید شامل کر دی گئی",
    analyzing: "رسید کا تجزیہ کیا جا رہا ہے...",
    apiKeyMissing: "اوپن اے آئی اے پی آئی کی موجود نہیں ہے۔ رسید کے تجزیہ کے لیے براہ کرم اسے ترتیبات میں شامل کریں۔",
    receiptAnalysisError: "رسید کا تجزیہ کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں یا فارم کو دستی طور پر بھریں۔",
    photoError: "تصویر لینے میں خرابی",
    imagePickError: "تصویر منتخب کرنے میں خرابی",
    selectCurrency: "کرنسی منتخب کریں",
    selectStaff: "عملہ منتخب کریں",
    selectStaffPlaceholder: "عملے کا رکن منتخب کریں",
    searchStaff: "عملہ تلاش کریں",
    updateExpense: "خرچہ اپڈیٹ کریں",
    allCategories: "تمام زمرے",
    selectMonth: "مہینہ منتخب کریں",
    description: "تفصیل",
    loading: "اخراجات لوڈ ہو رہے ہیں...",
    updateSuccess: "خرچہ اپڈیٹ ہو گیا",
    updateSuccessDetail: "خرچہ کامیابی سے اپڈیٹ ہو گیا ہے",
    updateError: "خرچہ اپڈیٹ کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔",
    deleteSuccess: "خرچہ حذف ہو گیا",
    deleteSuccessDetail: "خرچہ کامیابی سے حذف کر دیا گیا ہے",
    deleteError: "خرچہ حذف کرنے میں ناکام۔ براہ کرم دوبارہ کوشش کریں۔",
    deleteConfirmation: "کیا آپ واقعی اس خرچے کو حذف کرنا چاہتے ہیں؟",
    deleteTitle: "خرچہ حذف کریں",
    recentExpenses: "حالیہ اخراجات",
    expenseBreakdown: "اخراجات کی تقسیم",
    byCategory: "زمرے کے مطابق اخراجات",
    byFarm: "فارم کے مطابق",
    dateRange: "تاریخ کی حد",
    thisMonth: "اس مہینے",
    lastMonth: "پچھلے مہینے",
    last3Months: "پچھلے 3 مہینے",
    last6Months: "پچھلے 6 مہینے",
    thisYear: "اس سال",
    custom: "اپنی مرضی کے مطابق",
    from: "سے",
    to: "تک",
    apply: "لاگو کریں",
    reset: "ری سیٹ کریں",
    currency: "روپے",
    total: "کل",
    average: "اوسط",
    highest: "سب سے زیادہ",
    lowest: "سب سے کم",
    noExpensesInRange: "منتخب تاریخ کی حد میں کوئی اخراجات نہیں",
    exportData: "ڈیٹا برآمد کریں",
    importData: "ڈیٹا درآمد کریں",
    exportSuccess: "ڈیٹا کامیابی سے برآمد کر دیا گیا",
    importSuccess: "ڈیٹا کامیابی سے درآمد کر دیا گیا",
    exportError: "ڈیٹا برآمد کرنے میں ناکام",
    importError: "ڈیٹا درآمد کرنے میں ناکام",
    errors: {
      invalidAmount: "براہ کرم درست رقم درج کریں",
      categoryRequired: "براہ کرم زمرہ منتخب کریں",
      farmRequired: "براہ کرم فارم منتخب کریں",
      paymentMethodRequired: "براہ کرم ادائیگی کا طریقہ منتخب کریں",
      staffRequired: "براہ کرم عملے کا رکن منتخب کریں"
    },
    selectCategory: "زمرہ منتخب کریں",
    monthlyOverview: "ماہانہ جائزہ",
    searchCategories: "زمرے تلاش کریں",
    totalSpent: "کل خرچ",
    totalEntries: "کل اندراجات",

  },
  farm: {
    select: "فارم منتخب کریں",
    selectPlaceholder: "فارم منتخب کریں"
  },

  animal: {
    select: "جانور منتخب کریں",
    selectPlaceholder: "جانور منتخب کریں (اختیاری)"
  },
  symptoms: {
    title: 'علامات چیکر',
    searchPlaceholder: 'علامات تلاش کریں',
    selectedSymptomsTitle: 'منتخب علامات',
    selectedSymptomsCount: 'منتخب علامات ({{count}})',
    possibleConditionsTitle: 'ممکنہ بیماریاں',
    matchingSymptomsPrefix: 'ملتی جلتی علامات: ',
    allSymptomsTitle: 'تمام علامات',
    noSymptomsFound: 'کوئی علامات نہیں ملیں',

    // Symptoms from data/symptoms.ts
    symptom_s1_name: "بخار",
    symptom_s1_description: "جسم کا درجہ حرارت معمول سے زیادہ ہونا",
    symptom_s2_name: "کھانسی",
    symptom_s2_description: "پھیپھڑوں سے ہوا کا زبردستی اخراج",
    symptom_s3_name: "سستی",
    symptom_s3_description: "توانائی کی کمی، غیر معمولی تھکاوٹ",
    symptom_s4_name: "اسہال",
    symptom_s4_description: "پتلے، پانی والے پاخانے جو معمول سے زیادہ کثرت سے ہوں",
    symptom_s5_name: "قے",
    symptom_s5_description: "منہ کے ذریعے معدے کے مواد کا زبردستی اخراج",
    symptom_s6_name: "ناک سے بہاؤ",
    symptom_s6_description: "ناک سے سیال کا نکلنا",
    symptom_s7_name: "لنگڑا پن",
    symptom_s7_description: "چلنے یا کھڑے ہونے میں دشواری، لنگڑانا",
    symptom_s8_name: "وزن میں کمی",
    symptom_s8_description: "جسمانی وزن میں غیر ارادی کمی",

    // Diseases from data/symptoms.ts
    disease_d1_name: "منہ کھر کی بیماری",
    disease_d1_description: "کھر والے جانوروں کو متاثر کرنے والی انتہائی متعدی وائرل بیماری",
    disease_d1_treatment: "معاون دیکھ بھال، سوزش کم کرنے والی ادویات",
    disease_d1_prevention: "ویکسینیشن، حیاتیاتی تحفظ کے اقدامات",

    disease_d2_name: "مویشیوں کی سانس کی بیماری",
    disease_d2_description: "مویشیوں کو متاثر کرنے والی سانس کی بیماریوں کا مجموعہ",
    disease_d2_treatment: "اینٹی بائیوٹکس، سوزش کم کرنے والی دوائیں",
    disease_d2_prevention: "ویکسینیشن، مناسب وینٹیلیشن، تناؤ میں کمی",

    disease_d3_name: "تھنوں کی سوزش (ساڑو)",
    disease_d3_description: "دودھ دینے والے جانوروں میں تھنوں کی سوزش",
    disease_d3_treatment: "اینٹی بائیوٹکس، بار بار دودھ نکالنا، معاون دیکھ بھال",
    disease_d3_prevention: "دودھ دوہنے کی مناسب صفائی، تھنوں کی باقاعدہ جانچ",

    disease_d4_name: "بروسیلوسس (چھوت کی بیماری)",
    disease_d4_description: "متعدد اقسام کے جانوروں کو متاثر کرنے والا بیکٹیریل انفیکشن",
    disease_d4_treatment: "اینٹی بائیوٹکس، معاون دیکھ بھال",
    disease_d4_prevention: "ویکسینیشن، متاثرہ جانوروں کی جانچ اور انہیں الگ کرنا",
  },
  pregnancy: {
    loading:"حمل لوڈ ہو رہی ہے...",
    title: "حمل",
    expectedDate: "متوقع تاریخ",
    daysRemaining: "باقی دن",
    stage: "مرحلہ",
    progress: "پیشرفت",
    fullReport: "مکمل رپورٹ",
    dietPlan: "غذائی منصوبہ",
    treatment: "علاج",
    calendar: "کیلنڈر",
    addPregnancy: "حمل کا اندراج کریں",
    report: "رپورٹ",
    dietTab: "خوراک",
    healthTab: "صحت",
    statusT:"حمل کی حالت",
    details: "حمل کی تفصیلات",
    timeline: "ٹائم لائن",
    animal: "جانور",
    sire: "نر جانور",
    species: "نسل",
    conceptionDate: "حمل کی تاریخ",
    dietRecommendations: "غذائی تجاویز",
    dietSummary: "یہ {{species}} کا حمل فی الوقت ہفتہ {{currentWeek}} میں ہے تقریباً {{totalWeeks}} ہفتوں میں سے۔",
    healthChecks: "صحت کی جانچ",
    healthSummary: "یہ {{species}} کا حمل فی الوقت ہفتہ {{currentWeek}} میں ہے تقریباً {{totalWeeks}} ہفتوں میں سے۔",
    aiGenerated: "اے آئی سے تیار کردہ",
    stages: {
      early: "ابتدائی مرحلہ",
      mid: "درمیانی مرحلہ",
      late: "آخری مرحلہ"
    },
    noPregnancies: "ابھی تک کوئی حمل درج نہیں ہے۔",
    noPregnanciesMessage: "جانوروں کے حمل کی نگرانی کے لیے حمل کا ریکارڈ شامل کریں۔",
    addTitle: "نیا حمل شامل کریں",
    selectFemaleAnimal: "مادہ جانور منتخب کریں",
    selectFemalePlaceholder: "مادہ جانور منتخب کریں",
    selectSire: "نر جانور منتخب کریں",
    selectSirePlaceholder: "نر جانور منتخب کریں",
    aiCross: "اے آئی کراس",
    selectStatusPlaceholder: "حمل کی حالت منتخب کریں",
    selectFarm: "فارم منتخب کریں",
    selectSpecies: "نسل منتخب کریں",
    saveSuccess: "حمل کا ریکارڈ کامیابی سے محفوظ ہو گیا۔",
    selectStatus: "حالت منتخب کریں",
    useAIPlans: "اے آئی سے تیار کردہ منصوبے استعمال کریں",
    weekNumber: "ہفتہ {{week}}",
    current: "موجودہ",
    selectWeek: "ہفتہ منتخب کریں",
    searchStatus:"حالت تلاش کریں",
    searchSires:"نر جانور تلاش کریں",
    addSuccess:"حمل کا ریکارڈ شامل کر دیا گیا",
    addSuccessMessage:"حمل کا ریکارڈ کامیابی سے شامل کر دیا گیا",
    status: {
      active: "فعال",
      confirmed: "تصدیق شدہ",
      suspected: "مشتبہ",
      notPregnant: "حاملہ نہیں"
    },
    statuses: {
      confirmed: "تصدیق شدہ",
      suspected: "مشتبہ",
      not_pregnant: "حاملہ نہیں"
    },
    totalPregnancies: "کل حمل: {{count}}",
    clickToViewDetails: "تفصیلات دیکھنے کے لیے کلک کریں",
    viewAllPregnancies: "تمام حمل دیکھیں",
    health: {
      recommendedCheckups: "تجویز کردہ جانچ",
      recommendedTreatments: "تجویز کردہ علاج",
      weekTitle: "ہفتہ {{week}}: {{stage}} صحت کی توجہ ({{species}})",
      generalDescription: "{{species}} کے لیے {{stage}} مرحلے میں عمومی صحت کی تجاویز، ہفتہ {{week}}۔ صحت کی نگرانی کریں اور تشویش کی صورت میں ڈاکٹر سے رابطہ کریں۔",
      cow: {
        early: {
          checkup1: "حمل کی ویٹرنری تصدیق (دن 30-60 کے آس پاس الٹراساؤنڈ، ہفتہ {{week}})",
          checkup2: "جسمانی حالت کا اسکور",
          treatment1: "کلوسٹریڈیل ویکسینیشن (اگر ضروری ہو، ڈاکٹر سے مشورہ کریں، ہفتہ {{week}})",
          treatment2: "کیڑے مارنا (فیکل ٹیسٹ اور ڈاکٹر کے مشورے کی بنیاد پر)"
        },
        mid: {
          checkup1: "اسقاط حمل کی علامات کی نگرانی",
          checkup2: "جنین کی دل کی دھڑکن کی جانچ (اگر آلات دستیاب ہوں، ہفتہ {{week}})",
          treatment1: "بوسٹر ویکسینیشن (جیسے ScourGuard، ڈاکٹر سے مشورہ کریں، ہفتہ {{week}})",
          treatment2: "مکھیوں کے کنٹرول کے اقدامات"
        },
        late: {
          checkup1: "تھن کی نشوونما اور کولوسٹرم کی جانچ",
          checkup2: "شرونی کے لگامنٹ کی نرمی کا جائزہ (ہفتہ {{week}})",
          treatment1: "وٹامن E/سیلینیم انجیکشن (اگر کمی ہو، بچھڑے کی پیدائش سے 2-3 ہفتے پہلے، ہفتہ {{week}})",
          treatment2: "صاف، خشک بچھڑے کی پیدائش کا ماحول یقینی بنائیں"
        }
      },
      goat: {
        early: {
          checkup1: "حمل کی تشخیص (دن 25-45 کے آس پاس الٹراساؤنڈ، ہفتہ {{week}})",
          checkup2: "پیراسائٹ لوڈ کا جائزہ (فیکل ایگ کاؤنٹ)",
          treatment1: "CD&T ویکسینیشن (اگر اپ ٹو ڈیٹ نہیں، ڈاکٹر سے مشورہ کریں، ہفتہ {{week}})",
          treatment2: "حکمت عملی کے ساتھ کیڑے مارنا (اگر ضروری ہو)"
        },
        mid: {
          checkup1: "کیٹوسس/حمل کے زہریلے پن کی علامات کی نگرانی",
          checkup2: "جسمانی حالت کی جانچ (ہفتہ {{week}})",
          treatment1: "بوسٹر CD&T ویکسینیشن (بچے کی پیدائش سے 4 ہفتے پہلے، ہفتہ {{week}})",
          treatment2: "جوؤں/کیڑوں کا علاج (اگر ضروری ہو)"
        },
        late: {
          checkup1: "بچے کی پیدائش کے کٹ کی تیاری کی جانچ",
          checkup2: "آنے والی پیدائش کی علامات کا مشاہدہ (ہفتہ {{week}})",
          treatment1: "سیلینیم/وٹامن E سپلیمنٹ (اگر کمی والے علاقے میں، ہفتہ {{week}})",
          treatment2: "بچے کی پیدائش کا سامان تیار کریں (جراثیم کش، تولیے)"
        }
      },
      general: {
        checkup1: "عمومی صحت کا مشاہدہ (ہفتہ {{week}})",
        checkup2: "مخصوص تشویشات کے لیے ڈاکٹر سے مشورہ کریں",
        treatment1: "معمول کے صحت کے پروٹوکول کی پیروی کریں (ہفتہ {{week}})",
        treatment2: "صرف ڈاکٹر کی تجویز کردہ علاج دیں"
      }
    },
    diet: {
      cow: {
        early: {
          title: "ہفتہ {{week}}: بنیادی غذائیت",
          description: "ابتدائی جنین کی نشوونما کے لیے معیاری چارہ اور متوازن معدنیات پر توجہ دیں۔",
          nutrient1: "کیلشیم اور فاسفورس (1:1 تناسب)",
          nutrient2: "وٹامن اے اور ای",
          nutrient3: "معیاری پروٹین (12-14%)",
          food1: "اعلیٰ معیار کا گھاس یا چراگاہ (10-15 کلو/دن)",
          food2: "متوازن معدنی مکس (100-150 گرام/دن)",
          food3: "ضرورت کے مطابق محدود اناج (1-2 کلو/دن)"
        },
        mid: {
          title: "ہفتہ {{week}}: نشوونما کی مدد",
          description: "بڑھتے ہوئے جنین کی مدد اور جسمانی حالت برقرار رکھنے کے لیے توانائی کی مقدار بڑھائیں۔",
          nutrient1: "بڑھی ہوئی توانائی (TDN 60-65%)",
          nutrient2: "پروٹین (12-14%)",
          nutrient3: "ٹریس معدنیات (سیلینیم، کاپر)",
          food1: "معیاری چارہ (12-18 کلو/دن)",
          food2: "اعتدال پسند اناج کی تکمیل (2-3 کلو/دن)",
          food3: "ٹریس عناصر کے ساتھ معدنی مکس (150 گرام/دن)"
        },
        late: {
          title: "ہفتہ {{week}}: بچھڑے کی پیدائش کی تیاری",
          description: "دودھ کا بخار روکنے کے لیے زیادہ توانائی والی خوراک اور مناسب کیلشیم توازن کے ساتھ بچھڑے کی پیدائش کی تیاری کریں۔",
          nutrient1: "زیادہ توانائی (TDN 65-70%)",
          nutrient2: "متوازن کیلشیم (دودھ کا بخار روکیں)",
          nutrient3: "وٹامن ای اور سیلینیم",
          food1: "توانائی سے بھرپور خوراک (15-20 کلو/دن کل)",
          food2: "مناسب طریقے سے متوازن معدنیات (150-200 گرام/دن)",
          food3: "پھلیوں کے ساتھ معیاری گھاس (10-12 کلو/دن)"
        }
      },
      goat: {
        early: {
          title: "ہفتہ {{week}}: ابتدائی بچے کی پیدائش کی غذائیت",
          description: "ابتدائی جنین کی نشوونما کے لیے معیاری چارہ اور متوازن معدنیات فراہم کریں۔",
          nutrient1: "کیلشیم اور فاسفورس",
          nutrient2: "وٹامن اے",
          nutrient3: "معیاری پروٹین (14%)",
          food1: "چراگاہ اور چارہ (2-3 کلو/دن)",
          food2: "الفالفا گھاس (0.5-1 کلو/دن)",
          food3: "بکری کے لیے مخصوص معدنی مکس (25-30 گرام/دن)"
        },
        mid: {
          title: "ہفتہ {{week}}: درمیانی حمل کی مدد",
          description: "بکریوں میں عام طور پر متعدد جنین کی مدد کے لیے آہستہ آہستہ خوراک کا معیار بڑھائیں۔",
          nutrient1: "بڑھی ہوئی توانائی",
          nutrient2: "پروٹین (14-16%)",
          nutrient3: "کاپر اور سیلینیم",
          food1: "معیاری گھاس (2-3 کلو/دن)",
          food2: "محدود اناج مکس (0.5-0.75 کلو/دن)",
          food3: "کاپر کے ساتھ معدنی سپلیمنٹ (30 گرام/دن)"
        },
        late: {
          title: "ہفتہ {{week}}: آخری حمل کی تیاری",
          description: "حمل کے زہریلے پن کو روکنے اور حتمی جنین کی نشوونما کی مدد کے لیے توانائی سے بھرپور غذائیت پر توجہ دیں۔",
          nutrient1: "زیادہ توانائی کی کثافت",
          nutrient2: "کیلشیم (زہریلے پن کو روکیں)",
          nutrient3: "وٹامن ای",
          food1: "الفالفا گھاس (2-3 کلو/دن)",
          food2: "متوازن اناج مکس (0.75-1 کلو/دن)",
          food3: "توانائی کے ذریعے کے طور پر گڑ (50-100 گرام/دن)"
        }
      },
      sheep: {
        early: {
          title: "ہفتہ {{week}}: ابتدائی بھیڑ کے بچے کی پیدائش کی غذائیت",
          description: "معیاری چارہ اور معدنی سپلیمنٹ کے ساتھ اعتدال پسند جسمانی حالت برقرار رکھیں۔",
          nutrient1: "سیلینیم",
          nutrient2: "وٹامن ای",
          nutrient3: "معیاری پروٹین (12-14%)",
          food1: "معیاری چراگاہ/گھاس (1.5-2 کلو/دن)",
          food2: "بھیڑ کے لیے مخصوص معدنی مکس (20-25 گرام/دن)",
          food3: "محدود اناج (0.2-0.4 کلو/دن)"
        },
        mid: {
          title: "ہفتہ {{week}}: جنین کی نشوونما کی مدد",
          description: "اون کی نشوونما اور جنین کی نشوونما کی مدد کے لیے آہستہ آہستہ غذائیت بڑھائیں۔",
          nutrient1: "بڑھی ہوئی توانائی",
          nutrient2: "پروٹین (14%)",
          nutrient3: "کاپر (سطح کے ساتھ احتیاط)",
          food1: "مخلوط گھاس (2-2.5 کلو/دن)",
          food2: "اعتدال پسند اناج (0.4-0.6 کلو/دن)",
          food3: "کمی والے علاقوں میں سیلینیم سپلیمنٹ (تجویز کے مطابق)"
        },
        late: {
          title: "ہفتہ {{week}}: بھیڑ کے بچے کی پیدائش سے پہلے کی تیاری",
          description: "حمل کے زہریلے پن کو روکنے کے لیے توانائی کی مقدار بڑھائیں، خاص طور پر متعدد بچے لے جانے والی بھیڑوں کے لیے۔",
          nutrient1: "زیادہ توانائی کی کثافت",
          nutrient2: "کیلشیم",
          nutrient3: "وٹامن بی کمپلیکس",
          food1: "توانائی سے بھرپور خوراک (2.5-3 کلو/دن کل)",
          food2: "معیاری گھاس (1.5-2 کلو/دن)",
          food3: "متوازن اناج مکس (0.6-0.8 کلو/دن)"
        }
      },
      pig: {
        early: {
          title: "ہفتہ {{week}}: ابتدائی سور کے بچے کی پیدائش کی خوراک",
          description: "امپلانٹیشن کی مدد کے لیے متوازن پروٹین کے ساتھ اعتدال پسند خوراک کی مقدار برقرار رکھیں۔",
          nutrient1: "فولک ایسڈ",
          nutrient2: "معیاری پروٹین (12-14%)",
          nutrient3: "وٹامن اے",
          food1: "متوازن سور کی خوراک (2-2.5 کلو/دن)",
          food2: "تازہ پانی (مرضی کے مطابق)",
          food3: "محدود ٹریٹس (100-200 گرام/دن)"
        },
        mid: {
          title: "ہفتہ {{week}}: جنین کی نشوونما کی مدد",
          description: "بڑھتے ہوئے بچوں کی تعداد کی مدد کے لیے آہستہ آہستہ خوراک بڑھائیں۔",
          nutrient1: "بڑھا ہوا لائسین",
          nutrient2: "کیلشیم اور فاسفورس",
          nutrient3: "بی وٹامنز",
          food1: "بڑھا ہوا سور کا راشن (2.5-3 کلو/دن)",
          food2: "تازہ سبزیاں (0.5 کلو/دن)",
          food3: "صاف پانی (مرضی کے مطابق)"
        },
        late: {
          title: "ہفتہ {{week}}: بچے کی پیدائش سے پہلے کی تیاری",
          description: "بچے کی پیدائش سے پہلے قبض کو روکنے کے لیے فائبر برقرار رکھتے ہوئے خوراک کا معیار بڑھائیں۔",
          nutrient1: "فائبر",
          nutrient2: "بڑھی ہوئی توانائی",
          nutrient3: "وٹامن ای اور سیلینیم",
          food1: "زیادہ فائبر والی خوراک (3-3.5 کلو/دن)",
          food2: "چوکر کے سپلیمنٹس (0.2-0.3 کلو/دن)",
          food3: "بڑھا ہوا خوراک کا حجم (3.5-4 کلو/دن کل)"
        }
      },
      general: {
        title: "ہفتہ {{week}}: {{stage}} مرحلے کی غذائیت",
        description: "حمل کے اس مرحلے کے لیے موزوں متوازن غذائیت پر توجہ دیں۔",
        nutrient1: "متوازن معدنیات",
        nutrient2: "معیاری پروٹین",
        nutrient3: "ضروری وٹامنز",
        food1: "معیاری چارہ (جسمانی وزن کے مطابق ایڈجسٹ کریں)",
        food2: "متوازن خوراک مکس (جسمانی وزن کے مطابق ایڈجسٹ کریں)",
        food3: "تازہ پانی (مرضی کے مطابق)"
      },
      keyNutrients: "اہم غذائی اجزاء",
      recommendedFoods: "تجویز کردہ خوراک"
    },

    // Validation messages for pregnancy form
    farmRequired: "فارم ضروری ہے",
    femaleAnimalRequired: "مادہ جانور ضروری ہے",
    sireRequired: "نر جانور ضروری ہے",
    statusRequired: "پیداوار کی حالت منتخب کریں",
  },
  milking: {
    loading:"دودھ نکالنے کے ریکارڈز لوڈ ہو رہے ہیں...",
    title: "دودھ نکالنے کے ریکارڈز",
    addRecord: "دودھ نکالنے کا ریکارڈ شامل کریں",
    editRecord: "دودھ نکالنے کا ریکارڈ میں ترمیم کریں",
    recordDetails: "دودھ نکالنے کے ریکارڈ کی تفصیلات",
    noRecords: "دودھ نکالنے کے کوئی ریکارڈز نہیں",
    addYourFirstRecord: "پیداوار کو ٹریک کرنے کے لیے اپنا پہلا دودھ نکالنے کا ریکارڈ شامل کریں",
    noRecordsForFilter: "منتخب کردہ فلٹرز کے لیے کوئی ریکارڈ نہیں ملا",
    tryDifferentFilters: "اپنے فلٹرز کو ایڈجسٹ کریں یا نئے دودھ نکالنے کے ریکارڈز شامل کریں",
    averagePerRecord: "فی ریکارڈ اوسط",
    recentRecords: "حالیہ ریکارڈز",
    statistics: "دودھ نکالنے کے اعداد و شمار",
    totalProduction: "کل پیداوار",
    dailyAverage: "روزانہ اوسط",
    monthlyTrend: "ماہانہ رجحان",
    liters: "لیٹر",
    records: "ریکارڈز",
    quantity: "مقدار (لیٹر)",
    quality: "معیار",
    session: "سیشن",
    sessions: {
      morning: "صبح",
      afternoon: "دوپہر",
      evening: "شام"
    },
    qualities: {
      excellent: "بہترین",
      good: "اچھا",
      fair: "ٹھیک",
      poor: "خراب"
    },
    animal: "جانور",
    date: "تاریخ",
    notes: "نوٹس",
    milkedBy: "دودھ نکالنے والا",
    temperature: "درجہ حرارت (°C)",
    fat: "چربی %",
    protein: "پروٹین %",
    addSuccess: "دودھ نکالنے کا ریکارڈ کامیابی سے شامل کر دیا گیا",
    updateSuccess: "دودھ نکالنے کا ریکارڈ کامیابی سے اپ ڈیٹ کر دیا گیا",
    deleteSuccess: "دودھ نکالنے کا ریکارڈ کامیابی سے حذف کر دیا گیا",
    deleteConfirm: "کیا آپ واقعی اس دودھ نکالنے کے ریکارڈ کو حذف کرنا چاہتے ہیں؟",
    errors: {
      animalRequired: "جانور درکار ہے",
      quantityRequired: "مقدار درکار ہے",
      qualityRequired: "معیار درکار ہے",
      sessionRequired: "سیشن درکار ہے",
      dateRequired: "تاریخ درکار ہے",
      invalidFat: "براہ کرم درست چربی کی فیصد داخل کریں",
      invalidProtein: "براہ کرم درست پروٹین کی فیصد داخل کریں",
      invalidTemperature: "براہ کرم درست درجہ حرارت داخل کریں"
    }
  },
};
