export interface Farm {
  id: string;
  name: string;
  location: {
    address: string;
    latitude?: number;
    longitude?: number;
  };
  status: FarmStatus;
  ownerId: string; // ID of the user who created/owns the farm
  photoURL?: string; // URL to farm photo
  size?: number; // Size of the farm
  sizeUnit?: string; // Unit for farm size (Marla, Kanal, Acre, etc.)
  type?: string; // Type of farm (Live Stock, Mixed, etc.)
  description?: string; // Farm description
  animals?: string[]; // Array of animal IDs in this farm
  animalCount?: number; // Count of animals in this farm
  createdAt: number | string; // Support both timestamp and formatted date
  updatedAt: number | string; // Support both timestamp and formatted date
}

export enum FarmStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  COMPLETED = 'completed',
}
